::ng-deep {
    .formFieldOutline {
        flex-wrap: wrap;

        .mat-form-field-appearance-outline {
            .mat-form-field-infix {
                padding: 0.7em 0 0.9em 0;

                .mat-input-element {
                    font-family: <PERSON><PERSON><PERSON>,sans-serif!important;
                    /* font-weight: 600!important; */
                    font-size: 14px !important;
                    line-height: 14px !important;
                }
            }
            .mat-form-field-outline {
                background-color: #fafafa;
                border-radius: 5px;
            }
            .mat-form-field-outline-thick {
                color: #dddddd30;
            }
        }

        .mat-form-field-label-wrapper {
            top: -1em;
            font-size: 14px;
        }

        .mat-form-field.mat-focused {
            .mat-form-field-label {
                color: #ce7a58;
                font-size: 14px;
            }
        }

        .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float {
            .mat-form-field-label {
                color: #ce7a58;
                transform: translateY(-1.55em) scale(1);
                margin-bottom: 1em;
            }
        }
        
        .mat-form-field .error_Msg {
            font-size: 12px !important;
            float: right;
            display: flex;
            color: #ce7a58;
            .err {
                background-color: #f2a63494;
                border-radius: 50%;
                width: 1.2em;
                height: 1.2em;
                justify-content: center;
                display: flex;
                margin-left: 0.5em;
                .mat-icon {
                    color: #ce7a58;
                    vertical-align: middle;
                    align-self: center;
                    transform: scale(0.8);
                }
            }
        }

        .mat-select {
            line-height: 17px;
            .mat-select-trigger {
                font-size: 14px;
            }

            .mat-select-disabled {
                .mat-select-value {
                    color: #1e2f41;;
                }
            }
        }

        .searchBtn {
            margin-top: 0.3em;
            background-color: #ce7a58;
            color: #fff;
            height: 3.2em !important;
        }
    }
}
