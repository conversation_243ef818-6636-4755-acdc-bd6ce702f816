import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-signature-template',
  templateUrl: './signature-template.component.html',
  styleUrls: ['./signature-template.component.scss']
})
export class SignatureTemplateComponent implements OnInit {

  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<SignatureTemplateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SignatureTemplateDialog,
  ) { 
    this.signatures = data.signatures;
  }

  signatures: any[];
  currentIndex = 0;

  ngOnInit(): void {
  }

  onConfirm(){
    this.dialogRef.close(
      {
        status: true,
        data: this.signatures[this.currentIndex]
      }
    );
  }

  onCancel(){
    this.dialogRef.close(null);
  }

}

export class SignatureTemplateDialog {
  constructor(public signatures: any[]) {}
}