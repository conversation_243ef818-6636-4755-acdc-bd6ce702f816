import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {ApiProviderService} from "core/service/api-provider.service";
import {Observable} from "rxjs";
import {IFormOrginInFoInput, IFormOrginSave} from "shared/components/storage468/storage468.schema";
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Injectable({
  providedIn: 'root'
})
export class Storage468Service {
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private storage = this.apiProviderService.getUrl('digo', 'storage');
  private formOrginPath = `${this.storage}/form-origin`;
  private storage468 = this.deploymentService.getAppDeployment()?.storage468;
  constructor(private http: HttpClient,
              private apiProviderService: ApiProviderService,
              private deploymentService: DeploymentService) { }

  getPageFormOrgins(page?:number, size?:number, keyword?:string):Observable<any>{
    if(this.storage468 === undefined) return null;
    let URL = `${this.formOrginPath}?page=${page}&size=${size}`;
    if(keyword)
      URL += `&keyword=${keyword}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  uploadFileToStorage(identityNumber,formOrginId, fileId, filename): Observable<any>{
    if(this.storage468 === undefined) return null;
    let URL = `${this.padman}/dossier-form-file/--upload-to-storage/${fileId}?identity-number=${identityNumber}&filename=${filename}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(URL,{},{ headers });
  }

  saveFileToStorage(req?: IFormOrginInFoInput):Observable<any>{
    if(this.storage468 === undefined) return null;
    if(req){
      let URL = `${this.storage}/form-origin-info`;
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post(URL,req,{ headers });
    }else return null;
  }

  saveToStorage(req?: IFormOrginSave):Observable<any>{
    if(this.storage468 === undefined) return null;
    if(req){
      let URL = `${this.storage}/form-origin-info`;
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post(URL,req,{ headers });
    }else return null;
  }

  getFormOrginIns(formOrginIds:string[], identityNumber:string, fullname:string, birthday:string):Observable<any>{
    if(this.storage468 === undefined) return null;
    let queryFormOrgins = formOrginIds.join('&form-orgin-id=');
    let URL = `${this.storage}/form-origin-info?identity-number=${identityNumber}&fullname=${fullname}&birthday=${birthday}&form-orgin-id=${queryFormOrgins}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getDirectoryInfo(formOrginIds:string, identityNumber:string, fullname:string, birthday:string):Observable<any>{
    if(this.storage468 === undefined) return null;
    let URL = `${this.storage}/form-origin-info?identity-number=${identityNumber}&fullname=${fullname}&birthday=${birthday}&form-orgin-id=${formOrginIds}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  asyncStorageToFileman(files):Observable<any>{
    if(this.storage468 === undefined) return null;
    const strFile = [].concat(files).join(',');
    let URL = `${this.storage}/file/--async-to-fileman`;
    URL += `?file-id=${strFile}`;
    let headers = new HttpHeaders();
    return this.http.post(URL,{}, { headers });
  }

  getMapedIds(formCode:string[]):Promise<any>{
    return new Promise((resolve, rejects)=>{
      if(this.storage468 === undefined) return null;
      let queryFormOrgins = formCode.join('&form-code=');
      let URL = `${this.storage}/form-origin/--get-maped-ids?form-code=${queryFormOrgins}`;
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      this.http.get(URL, { headers }).subscribe(rs=>{
        resolve(rs);
      });
    });
  }

  getFormOutput(procerureId): Observable<any>{
    if(this.storage468 === undefined) return null;
    const URL = `${this.basepad}/procedure-form/--find-output/${procerureId}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getOriginTemplate(id: string): Observable<any> {
    let URL = `${this.storage}/origin-template/${id}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }

  getFamilyEformInput(eformId: string, role: string, identityNumber: string): Observable<any> {
    let URL = `${this.storage}/user-properties/family/eform-input`;
    URL += `?eform-id=${eformId}`;
    URL += `&role=${role}`;
    URL += `&identity-number=${identityNumber}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }

  saveUserProperties(dossierCode: string, eformId: string, data: any) {
    let URL = `${this.storage}/user-properties/--save`;
    URL += `?dossier-code=${dossierCode}`;
    URL += `&eform-id=${eformId}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(URL, data, { headers });
  }

}
