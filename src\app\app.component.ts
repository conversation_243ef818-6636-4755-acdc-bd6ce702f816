import { BasecatService } from 'src/app/data/service/svc-basecat/basecat.service';
import { Component, OnInit, LOCALE_ID, Inject, Renderer2 } from '@angular/core';
import { EnvService } from './core/service/env.service';
import { Title } from '@angular/platform-browser';
import { KeycloakService } from 'keycloak-angular';
import { MainService } from './data/service/main/main.service';
import { DOCUMENT } from '@angular/common';
import { DeploymentService } from './data/service/deployment.service';
import { ActivatedRoute, ActivatedRouteSnapshot, NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import {LogmanService} from 'data/service/logman/logman.service';
import {HttpClient} from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { error } from 'console';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  currentRoute: string;
  isShowWidgetZaloKtm = this.env?.OS_KTM?.isShowWidgetZaloKtm || false;
  isMessengerPlugin = this.deploymentService.env.OS_QNM.isMessengerPlugin;
  isMaintenancePeriod = this.deploymentService.isMaintenancePeriod;  
  //DNI
  isHomeDNI = this.deploymentService.getAppDeployment()?.isHomeDNI || false;
  ShowTheNewInterfaceDVCTP = this.deploymentService?.getAppDeployment()?.ShowTheNewInterfaceDVCTP;
  uiLoaderConfig = environment.config.uiLoaderConfig;
  isNotPostUserEventsLog = this.deploymentService.getAppDeployment()?.isNotPostUserEventsLog == true ? true : false;
  iPCheckUrl = this.deploymentService.newConfigV2.iPCheckUrl;

  constructor(
    private envService: EnvService,
    private keycloak: KeycloakService,
    private mainService: MainService,
    private deploymentService: DeploymentService,
    @Inject(LOCALE_ID) protected localeId: string,
    @Inject(DOCUMENT) private doc: any,
    private titleService: Title,
    private router: Router,
    private logmanService: LogmanService,
    private http: HttpClient,
    private baseCatService: BasecatService,
    private renderer: Renderer2
  ) {
    this.deploymentService.setAppDeployment();
  }

  async ngOnInit(): Promise<void> {
    if(this.isHomeDNI){
      this.renderer.addClass(this.doc.body, 'layoutDNI');
    }
    this.router.events.pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(event => {
        // tslint:disable-next-line: no-string-literal
        this.currentRoute = event['url'];
         this.integrateChatbox();
      });
    localStorage.setItem('language', this.localeId);
    if (this.localeId === 'vi') {
      localStorage.setItem('languageId', '228');
      localStorage.setItem('locale', 'vi-VN');
      this.titleService.setTitle(this.config.siteTitle.vi);
    }
    if (this.localeId === 'en') {
      localStorage.setItem('languageId', '46');
      localStorage.setItem('locale', 'en-US');
      this.titleService.setTitle(this.config.siteTitle.en);
    }

    if (localStorage.getItem('siteName') === '' || localStorage.getItem('siteName') === null ) {
      if (this.localeId === 'vi') {
        localStorage.setItem('siteName', this.config.rootAgency.trans.vi.name);
      }
      if (this.localeId === 'en') {
        localStorage.setItem('siteName', this.config.rootAgency.trans.en.name);
      }
    }
    if (localStorage.getItem('selectedAgencyId') === null ) {
      localStorage.setItem('selectedAgencyId', '');
      localStorage.setItem('selectedAgencyName', '');
    }
    this.generateTrackingBTTT();

    this.generateGoogleAnalytics();
    this.generateGoogleAnalyticsBTTTT();
    if(this.isMaintenancePeriod) {
      if(window.location.href.includes("isMaintenanceAccess=true"))
      {
        localStorage.setItem('isMaintenanceAccess', "true");
      }else if(window.location.href.includes("isMaintenanceAccess=false")){
        localStorage.setItem('isMaintenanceAccess', "false");
      }
      this.getMaintenancePeriodCurrent();
    }
    if(this.ShowTheNewInterfaceDVCTP === 1){
      this.updateUiLoaderConfig();
    }
    if (this.deploymentService.getAppDeployment()?.env?.enableSmartUX == 1){
      this.integrateSmartUX();
    }
  }
  integrateSmartUX(){
    console.log(this.deploymentService.getAppDeployment()?.SmartUXScript);
    let decodedScript = this.htmlDecode(this.deploymentService.getAppDeployment()?.SmartUXScript);
        decodedScript = decodedScript.replace(/<script>/g, '').replace(new RegExp('</script>', 'g'), '');
        const script = this.doc.createElement('script');
        script.type = 'text/javascript';
        script.innerHTML = decodedScript;
        const head = this.doc.getElementsByTagName('head')[0];
        head.appendChild(script);

  }
  updateUiLoaderConfig() {
    this.uiLoaderConfig.fgsColor = '#007aff';
  }
  getMaintenancePeriodCurrent() {
    this.baseCatService.getMaintenancePeriodCurrent().subscribe(res => {
      if(res.length > 0) {
        let isMaintenanceAccess = localStorage.getItem("isMaintenanceAccess")
        if(isMaintenanceAccess !== "true") {
          this.router.navigate(['maintenance']);
        }
      }
    },error => {

    })
  }
  getClientIp() {
    this.http.get<{ ip: string }>(this.iPCheckUrl)
      .subscribe(data => {
        localStorage.setItem('clientIP', data.ip);
      });
  }

  generateTrackingBTTT(){
    const trackingCode = JSON.parse(localStorage.getItem('deploymentVariables'))?.configuration?.trackingBTTTT;
    if(!!trackingCode){
      const script = this.doc.createElement('script');
      script.type = 'text/javascript';
      script.innerHTML = this.htmlDecode(trackingCode);
      const head = this.doc.getElementsByTagName('head')[0];
      head.appendChild(script);
      // luu log tracking emc
      if (!this.isNotPostUserEventsLog){
        this.logmanService.postUserEventsLog("emcTracking", {}).subscribe();
      }
    }
  }

  htmlDecode(input) {
    const doc = new DOMParser().parseFromString(input, 'text/html');
    return doc.documentElement.textContent.toString();
  }

  async integrateChatbox(){
    const enableChatbot = this.deploymentService.env.integration.enableChatbot;
    let disableChatbot = false;
    const chatbotExcludeUrls = this.env?.chatbotExcludeUrls ? this.env?.chatbotExcludeUrls : this.envService.getConfig().chatbotExcludeUrls;
    for (const url of chatbotExcludeUrls) {
      if (this.currentRoute.includes(url)) {
        disableChatbot = true;
        break;
      }
    }
    const error = 0;
    if (enableChatbot === 1 && !disableChatbot){
      const encodedScript = this.deploymentService.getAppDeployment()?.chatbotScript;
      if (encodedScript){
        let decodedScript = this.htmlDecode(encodedScript);
        decodedScript = decodedScript.replace(/<script>/g, '').replace(new RegExp('</script>', 'g'), '');
        const script = this.doc.createElement('script');
        script.type = 'text/javascript';
        script.innerHTML = decodedScript;
        const head = this.doc.getElementsByTagName('head')[0];
        head.appendChild(script);
      }
    }
  }

  generateGoogleAnalytics(){
    if (localStorage.getItem("deploymentVariables") === null) {
      setTimeout(() => {
        this.generateGoogleAnalytics();
      }, 1000);
    }
    else {
      let config = JSON.parse(localStorage.getItem('deploymentVariables'))?.configuration;
      let sourceCode = config?.googleAnalytics?.source;
      if(!!sourceCode){
        let script1 = this.doc.createElement('script');
        script1.async = true;
        script1.src = sourceCode;
        let head = this.doc.getElementsByTagName('head')[0];
        head.appendChild(script1);
      }

      let embedCode = config?.googleAnalytics?.script;
      if(!!embedCode){
        let script2 = this.doc.createElement('script');
        script2.innerHTML = this.htmlDecode(embedCode);
        let head = this.doc.getElementsByTagName('head')[0];
        head.appendChild(script2);
      }
    }

  }
  //IGATESUPP-102629-iGate Bộ 4T: Hỗ trợ nhúng Google tag (gtag.js) cho EMC trên Cổng DVC
  generateGoogleAnalyticsBTTTT(){
    if (localStorage.getItem("deploymentVariables") === null) {
      setTimeout(() => {
        this.generateGoogleAnalyticsBTTTT();
      }, 1000);
    }
    else {
      let config = JSON.parse(localStorage.getItem('deploymentVariables'))?.configuration;
      let sourceCode = config?.googleAnalyticsBTTTT?.source;
      if(!!sourceCode){
        let script1 = this.doc.createElement('script');
        script1.async = true;
        script1.src = sourceCode;
        let head = this.doc.getElementsByTagName('head')[0];
        head.appendChild(script1);
      }

      let embedCode = config?.googleAnalyticsBTTTT?.script;
      if(!!embedCode){
        let script2 = this.doc.createElement('script');
        script2.innerHTML = this.htmlDecode(embedCode);
        let head = this.doc.getElementsByTagName('head')[0];
        head.appendChild(script2);
      }
    }
  }

}
