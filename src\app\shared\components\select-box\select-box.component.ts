import { Component, EventEmitter, Input, OnChanges, OnInit, Output, Self, SimpleChanges } from '@angular/core';
import { ControlValueAccessor, NgControl } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SelectBoxService } from 'src/app/data/service/shared/select-box.service';

@Component({
  selector: 'digo-select-box',
  templateUrl: './select-box.component.html',
  styleUrls: ['./select-box.component.scss']
})
export class SelectBoxComponent implements OnInit, ControlValueAccessor, OnChanges {

  @Input() label: string = 'Chọn';
  @Input() type: string = 'tag';
  @Input() parent: string = '5f3a491c4e1bd312a6f00055';
  @Input() required: boolean = false;
  @Input() search: boolean = true;
  @Input() disabled: boolean = false;
  @Output() itemSelectionChange = new EventEmitter<any>();
  option = {
    selected: null,
    list: [],
    keyword: '',
    calling: false,
    totals: 0,
    page: 0
  }
  timeout: any = null;
  reloadTimeOut = !!this.envService.getConfig()?.reloadTimeout ? this.envService.getConfig()?.reloadTimeout : 2000;

  constructor(
    private service: SelectBoxService,
    @Self() public ngControl: NgControl,
    private envService: EnvService,
  ) { 
    this.ngControl.valueAccessor = this;
  }

  ngOnInit(): void {
    // this.getList(this.option.keyword, this.option.page);
    // console.log(this.disabled);
  }

  ngOnChanges(changes: SimpleChanges) {
    this.option.calling = true;
    this.option.list = [];
    this.option.page = 0;
    this.getList(this.option.keyword, this.option.page);
    console.log(this.label, 'changes');
  }

  async getList(keyword, page) {
    console.log(this.label, );
    
    this.option.calling = true;
    console.log(this.parent);
    console.log(this.type);
    const lst = await this.service.callApi(this.type, this.parent, keyword, page).toPromise();
    Array.prototype.push.apply(this.option.list, lst?.content);
    this.option.totals = lst.totalPages;
    this.option.calling = false;
    // this.service.callApi(this.type, this.parent, keyword, page).subscribe(data => {
    //   for (const item of data.content) {
    //     if (item.name) {
    //       this.option.list.push(item);
    //     }
    //   }
    //   this.option.totals = data.totalPages;
    //   this.option.calling = false;
    // }, err => {
    //   console.log('shared list component error:', err);
    // });
    console.log(this.label, this.option.selected);
    console.log(this.label, this.option.list);
    console.log(this.label, this.option.list.find(i => i.id === this.option.selected));
    
    if(this.ngControl.control.value != this.option.list.find(i => i.id === this.option.selected)) {
      this.ngControl.control.setValue(this.option.list.find(i => i.id === this.option.selected));
    }
  }

  resetForm() {
    this.option.calling = true;
    this.option.list = [];
    this.option.page = 0;
    this.option.keyword = '';
    this.getList('', 0);
  }

  onEnter(event) {
    this.option.calling = true;
    clearTimeout(this.timeout);
    this.timeout = setTimeout( () => {
      this.option.list = [];
      this.option.page = 0;
      this.option.keyword = event.target.value;
      this.getList(this.option.keyword.trim(), this.option.page);
    }, this.reloadTimeOut);
  }

  getNextPage() {
    this.getList(this.option.keyword.trim(), ++this.option.page);
  }

  selectedItem(e) {
    console.log(e);
    
    const chosenItem = this.option.list.find(i => i.id === e.value);
    console.log(this.label, 'selected', chosenItem);
    
    this.ngControl.control.setValue(chosenItem);
    this.itemSelectionChange.emit(chosenItem);
  }

  async writeValue(obj: any) {
    console.log('write', obj);
    console.log(this.option.selected);
    console.log(this.option.list.length);
    if(this.option.selected === '' || this.option.selected === null || this.option.selected === undefined){
      console.log('set new value');
      
      if(obj?.id != '' && obj?.id != null && obj?.id != undefined){
        this.option.selected = obj?.id;
        // const chosenItem = this.option.list.find(i => i.id === obj?.id);
        // this.ngControl.control.setValue(chosenItem);
        if(this.ngControl.control.value != this.option.list.find(i => i.id === obj?.id)) {
          this.ngControl.control.setValue(this.option.list.find(i => i.id === obj?.id));
        }
        // this.ngControl.control.setValue(this.option.list.find(i => i.id === obj?.id));
      } else {
        this.option.selected = obj;
        console.log(this.option.list);
        
        const chosenItem = this.option.list.find(i => i.id == obj)
        console.log(chosenItem);
        
        // this.ngControl.control.setValue(chosenItem);
        if(this.ngControl.control.value != this.option.list.find(i => i.id === obj)) {
          this.ngControl.control.setValue(this.option.list.find(i => i.id === obj));
        }
        // this.ngControl.control.setValue(this.option.list.find(i => i.id === obj));
      }
    }
  }

  registerOnChange(fn: any) { }

  registerOnTouched(fn: any) { }

  setDisabledState?(isDisabled: boolean) { }

}
