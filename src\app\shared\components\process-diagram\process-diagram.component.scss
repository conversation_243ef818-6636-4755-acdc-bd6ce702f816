.diagram-container {
  height: 100%;
  ::ng-deep .bjs-powered-by {
    display: none;
  }
}

.btn-control {
  position: absolute;
  padding: 0.5em;
  display: grid;
  z-index: 1;
}

.btn-control button {
  margin: 0.2em 0;
  background-color: #fff;
  color: #1e2f41;
  box-shadow: 0 0 7px rgba(69, 65, 78, 0.28);
}

::ng-deep .highlight-overlay {
  background-color: green; /* color elements as green */
  opacity: 0.4;
  pointer-events: none; /* no pointer events, allows clicking through onto the element */
}

::ng-deep .highlight-red {
  //background-color: red; /* color elements as green */
  color: red;
  text-align: center;
  font-weight: 500;
  opacity: 1;
  pointer-events: none; /* no pointer events, allows clicking through onto the element */
}
