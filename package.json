{"name": "padsvc", "version": "0.0.0", "scripts": {"ng": "ng", "sonar": "sonar-scanner", "xi18n": "ng xi18n", "start": "ng serve", "build": "ng build", "build-i18n": "ng build --configuration=production-vi && ng build --configuration=production-en", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "mserve": "set NODE_OPTIONS=--max_old_space_size=8192 && ng serve --open"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^2.0.4", "@angular-material-components/moment-adapter": "^2.0.2", "@angular/animations": "~9.1.3", "@angular/cdk": "^9.2.4", "@angular/common": "~9.1.3", "@angular/compiler": "~9.1.3", "@angular/core": "~9.1.3", "@angular/elements": "^10.1.6", "@angular/flex-layout": "^9.0.0-beta.28", "@angular/forms": "~9.1.3", "@angular/http": "^7.2.16", "@angular/localize": "^9.1.4", "@angular/material": "^9.2.4", "@angular/platform-browser": "~9.1.3", "@angular/platform-browser-dynamic": "~9.1.3", "@angular/router": "~9.1.3", "@ckeditor/ckeditor5-angular": "^4.0.0", "@ckeditor/ckeditor5-build-classic": "^35.3.2", "@ckeditor/ckeditor5-build-decoupled-document": "^35.3.1", "@ckeditor/ckeditor5-editor-decoupled": "^35.3.1", "@ckeditor/ckeditor5-source-editing": "^35.3.2", "@ckeditor/ckeditor5-style": "^35.3.2", "@mapbox/mapbox-gl-geocoder": "^4.7.1", "@ngx-loading-bar/core": "^5.1.0", "@ngx-loading-bar/http-client": "^5.1.0", "@ngx-loading-bar/router": "^5.1.0", "@types/ckeditor": "^4.9.10", "angular-circliful": "^0.1.4-beta", "angular-formio": "^4.11.5", "angular-formio.css": "^1.0.1", "angular-highcharts": "^7.0.1", "angular-material": "^1.1.24", "angular2-qrcode": "^2.0.3", "bpmn-js": "^7.2.0", "chart.js": "^2.9.4", "chartjs-plugin-labels": "^1.1.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "highcharts": "^6.2.0", "html-to-pdfmake": "^2.0.10", "jwt-decode": "^2.2.0", "keycloak-angular": "^7.2.0", "keycloak-js": "^9.0.3", "leaflet": "^1.2.0", "mapbox-gl": "^2.3.1", "ng-mat-select-infinite-scroll": "^2.1.1", "ng2-charts": "^2.4.3", "ng2-ckeditor": "^1.3.7", "ng2-pdf-viewer": "^7.0.2", "ngx-cookie-service": "^12.0.3", "ngx-gallery-9": "^1.0.6", "ngx-image-compress": "^8.0.4", "ngx-infinite-scroll": "^10.0.0", "ngx-mask": "^10.0.1", "ngx-mat-select-search": "^3.1.0", "ngx-owl-carousel-o": "^3.1.1", "ngx-pagination": "^5.1.0", "ngx-printer": "^0.9.1", "ngx-ui-loader": "^10.0.0", "ngx-youtube-player": "^6.0.0", "pdfmake": "^0.1.68", "rxjs": "~6.5.4", "stream": "0.0.2", "sweetalert2": "^11.2.1", "timers": "^0.1.1", "tslib": "^1.10.0", "zone.js": "~0.10.2", "ngx-extended-pdf-viewer": "^10.5.0", "jspdf": "^2.3.1", "html2pdf.js": "^0.9.3", "ngx-onlyoffice": "^0.0.9", "slick-carousel": "^1.8.1", "ngx-slick-carousel": "^0.5.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.901.3", "@angular/cli": "~9.1.3", "@angular/compiler-cli": "~9.1.3", "@angular/language-service": "~9.1.3", "@ckeditor/ckeditor5-alignment": "^35.3.2", "@types/jasmine": "~3.5.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.12.47", "@types/pdfmake": "^0.1.14", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~3.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "~5.4.3", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~3.8.3", "jquery": "^3.6.0"}}