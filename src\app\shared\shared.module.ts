import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from './material.module';
import { SilentCheckSsoComponent } from './components/silent-check-sso/silent-check-sso.component';
import { HttpClientModule } from '@angular/common/http';
import { CustomSnackbarComponent } from './components/custom-snackbar/custom-snackbar.component';
import { environment } from 'src/environments/environment';
import { FlexLayoutModule } from '@angular/flex-layout';
import { NgxPaginationModule } from 'ngx-pagination';
import { LoadingBarRouterModule } from '@ngx-loading-bar/router';
import { LoadingBarHttpClientModule } from '@ngx-loading-bar/http-client';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { ErrorComponent } from './components/dialogs/error/error.component';
import { AlertComponent } from './components/dialogs/alert/alert.component';
import { LoadingComponent } from './components/dialogs/loading/loading.component';
import { MatSelectInfiniteScrollModule } from 'ng-mat-select-infinite-scroll';
import { SnackbarComponent } from './components/snackbar/snackbar.component';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { NgxUiLoaderModule } from 'ngx-ui-loader';
import { ProcessDiagramComponent } from './components/process-diagram/process-diagram.component';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { CustomDateAdapter } from './ts/custom-date-adapter';
import { MapboxComponent } from './components/mapbox/mapbox.component';
import { MapsViewComponent } from './modules/maps-view/maps-view.component';
import { MapboxPopupComponent } from './modules/maps-view/dialogs/mapbox-popup/mapbox-popup.component';
import { PaginationSliceComponent } from './components/pagination-slice/pagination-slice.component';
import { CheckSendNotifyComponent } from './components/check-send-notify/check-send-notify.component';
import { PdfViewerComponent } from './components/pdf-viewer/pdf-viewer.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { LoginViewComponent } from './components/login-view/login-view.component';
import { ConfirmComponent } from './components/dialogs/confirm/confirm.component';
import { SignatureTemplateComponent } from './components/signature-template/signature-template.component';
import { SelectBoxComponent } from './components/select-box/select-box.component';
import { DigoTableComponent } from './components/digo-table/digo-table.component';
import { TableConfigComponent } from './components/digo-table/table-config/table-config.component';
import { DigoDatePickerComponent } from './components/digo-date-picker/digo-date-picker.component';
import { ViettelPhoneNumberInputComponent } from './components/viettel-phone-number-input/viettel-phone-number-input.component';
import {ChooseFormorginComponent} from "shared/components/choose-formorgin/choose-formorgin.component";
import { ViewProcessDiagramComponent } from './components/view-process-diagram/view-process-diagram.component';
import { ProcessHandleComponent } from './components/process-handle/process-handle.component';
import { GetFormOrginComponent } from './components/storage468/get-form-storage468/get-form-orgin/get-form-orgin.component';
import { BtnGetFormOrginComponent } from './components/storage468/get-form-storage468/btn-get-form-orgin/btn-get-form-orgin.component';
import {ViewSignHistoryComponent} from "shared/components/view-sign-history/view-sign-history.component";
import {ViewSignHistoryDialogComponent} from "shared/components/view-sign-history/view-sign-history-dialog/view-sign-history-dialog.component";
import { NEACTemplateComponent } from './components/neac-template/neac-template.component';
import { ConfirmReportComponent } from './components/dialogs/confirm-report/confirm-report.component';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { GetFormOriginItemComponent } from './components/storage468/get-form-origin-item/get-form-origin-item.component';
import { MatIconModule } from '@angular/material/icon';
import { PopUpNotificationComponent } from './components/pop-up-notification/pop-up-notification.component';
import {Nd13PopupComponent} from "shared/components/nd13-popup/nd13-popup.component";
import { PerformaceWayComponent} from 'shared/components/performace-way/performace-way.component';
import {ProcedureFrequentComponent} from 'shared/components/procedure-frequent/procedure-frequent.component'
const ngxUiLoaderConfig: any = environment.config.uiLoaderConfig;
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';


//KGG OS
import { ButtonScannerBarcodeComponent } from './components/button-scanner-barcode/button-scanner-barcode.component';
import {OffWebPadsvcNotifyPopupComponent} from 'shared/components/off-web-padsvc-notify-popup/off-web-padsvc-notify-popup.component';
import {NotifyNotFoundPopupComponent} from 'shared/components/notify-not-found-dossier-popup/notify-not-found-popup.component';
import { SelectBoxDialogComponent } from './components/dialogs/select-box-dialog/select-box-dialog.component';

@NgModule({
  declarations: [
    SilentCheckSsoComponent,
    CustomSnackbarComponent,
    ErrorComponent,
    AlertComponent,
    SnackbarComponent,
    LoadingComponent,
    ProcessDiagramComponent,
    MapboxComponent,
    MapsViewComponent,
    MapboxPopupComponent,
    PaginationSliceComponent,
    CheckSendNotifyComponent,
    PdfViewerComponent,
    LoginViewComponent,
    SignatureTemplateComponent,
    ConfirmComponent,
    SelectBoxComponent,
    DigoTableComponent,
    TableConfigComponent,
    DigoDatePickerComponent,
    ViettelPhoneNumberInputComponent,
    ChooseFormorginComponent,
    ProcessHandleComponent,
    ViewProcessDiagramComponent,
    GetFormOrginComponent,
    BtnGetFormOrginComponent,
    ViewSignHistoryComponent,
    ViewSignHistoryDialogComponent,
    NEACTemplateComponent,
    ConfirmReportComponent,
    GetFormOriginItemComponent,
    PopUpNotificationComponent,
    Nd13PopupComponent,
    ButtonScannerBarcodeComponent,
    PerformaceWayComponent,
    ProcedureFrequentComponent,
    OffWebPadsvcNotifyPopupComponent,
    NotifyNotFoundPopupComponent,
    SelectBoxDialogComponent,
  ],
  imports: [
    CommonModule,
    MaterialModule,
    HttpClientModule,
    FlexLayoutModule,
    NgxPaginationModule,
    LoadingBarRouterModule,
    LoadingBarHttpClientModule,
    NgxMatSelectSearchModule,
    CarouselModule,
    MatSelectInfiniteScrollModule,
    InfiniteScrollModule,
    PdfViewerModule,
    CKEditorModule,
    NgxExtendedPdfViewerModule,
    NgxUiLoaderModule.forRoot(ngxUiLoaderConfig)
  ],
  exports: [
    CommonModule,
    MaterialModule,
    HttpClientModule,
    FlexLayoutModule,
    NgxPaginationModule,
    LoadingBarRouterModule,
    LoadingBarHttpClientModule,
    NgxMatSelectSearchModule,
    CarouselModule,
    MatSelectInfiniteScrollModule,
    InfiniteScrollModule,
    NgxUiLoaderModule,
    ProcessDiagramComponent,
    PaginationSliceComponent,
    CheckSendNotifyComponent,
    SelectBoxComponent,
    DigoTableComponent,
    DigoDatePickerComponent,
    ChooseFormorginComponent,
    ProcessHandleComponent,
    ViewProcessDiagramComponent,
    GetFormOrginComponent,
    BtnGetFormOrginComponent,
    ViewSignHistoryComponent,
    ViewSignHistoryDialogComponent,
    ConfirmReportComponent,
    CKEditorModule,
    GetFormOriginItemComponent,
    PopUpNotificationComponent,
    Nd13PopupComponent,
    ButtonScannerBarcodeComponent,
    PerformaceWayComponent,
    ProcedureFrequentComponent,
    OffWebPadsvcNotifyPopupComponent,
    NotifyNotFoundPopupComponent
  ],
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'vi-VN' },
    { provide: DateAdapter, useClass: CustomDateAdapter }
  ]
})
export class SharedModule { }
