<div class="digoSelectBox">
    <mat-form-field appearance="outline" class="w100">
        <mat-label> {{ label }} </mat-label>
        <mat-select [(ngModel)]="option.selected"
                required="{{ required }}"
                (selectionChange)="selectedItem($event)"
                msInfiniteScroll
                (infiniteScroll)="getNextPage()"
                [complete]="option.page >= option.totals"
                [disabled]="disabled">
            <div *ngIf="search" fxLayout="row" fxLayoutAlign="start center">
                <input matInput #searchInput (keyup)="onEnter($event)" (keydown)="$event.stopPropagation()"
                    placeholder="Nhập từ khóa" class="searchNested" />
                <button mat-icon-button class="spinnerBtn" *ngIf="option.calling">
                    <mat-spinner class="listSpinner"></mat-spinner>
                </button>
                <button mat-icon-button class="clearSearchNested" *ngIf="searchInput.value !== ''"
                    (click)="searchInput.value = ''; resetForm()">
                    <mat-icon> close </mat-icon>
                </button>
            </div>
            <mat-option *ngIf="option?.list?.length && !required"></mat-option>
            <mat-option *ngFor="let item of option?.list" [value]="item.id"> {{ item.name }} </mat-option>
        </mat-select>
    </mat-form-field>
    <mat-error *ngIf="required && ngControl.hasError('required') && ngControl.control.touched" class="input-err mt-15"
        fxLayout='row'>
        <ng-container>Vui lòng chọn</ng-container>&nbsp;
        <ng-container class="tt-lowercase">{{ label }}</ng-container>
    </mat-error>
</div>