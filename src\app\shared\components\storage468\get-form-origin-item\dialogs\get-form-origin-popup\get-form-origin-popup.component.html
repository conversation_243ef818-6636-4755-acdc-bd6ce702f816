<div class="all-content">
    <button mat-icon-button class="close-button" (click)="onDismiss()">
        <mat-icon>close</mat-icon>
    </button>
    <p class="procedureName">{{Element?.procedureName}}</p>
    <iframe *ngIf="storageLan == false" title="domain-kho-iframe" [src]="domainKhoTTHC" width="100%" height="100%"></iframe>

    <div *ngIf="storageLan == true && Element" style="padding-left: 1%; max-height: 500px;">
        <div style="margin-left: 20px;">
            <div>
                <span class="label">Mã thủ tục:</span>&nbsp;
                <i class="value">{{ Element.nationalProcedureCode }}</i>
            </div>
            <div>
                <span class="label">Tên thủ tục:</span>&nbsp;
                <i class="value">{{ Element.procedureName }}</i>
            </div>
            <div>
                <span class="label">Ch<PERSON> hồ sơ:</span>&nbsp;
                <i class="value">{{ fullname }} (CMND/CCCD: {{ identityNumber }})</i>
            </div>
        </div>
        <div fxLayout="row" class="tabs">
            <mat-tab-group (selectedTabChange)="onTabChange($event)" [selectedIndex]="tabIndex">
                <mat-tab label="Kho DVC Tỉnh"></mat-tab>
                <mat-tab label="Kho DVC Quốc Gia"></mat-tab>
            </mat-tab-group>
        </div>
        <mat-card style="margin: 0 20px;" class="formKhoLan" *ngIf="tabSelected == 0">
            <div *ngIf="dataFromProvince.length == 0">
                Không có dữ liệu
            </div>
            <div *ngFor="let item of dataFromProvince">
                <div class="titleTable">
                    <i>+ {{ item.MaGiayTo }}: {{ item.TenGiayTo }}</i>
                </div>
                <mat-card-content class="card-body">
                    <div *ngIf="item.DanhSachTapTinDinhKem.length == 0" class="mat-elevation-z8">
                        <span>Không có dữ liệu</span>
                    </div>
                    <div *ngIf="item.DanhSachTapTinDinhKem.length > 0">
                        <table mat-table [dataSource]="item.DanhSachTapTinDinhKem" class="responsive-table mat-elevation-z8">
                            <ng-container matColumnDef="index">
                                <mat-header-cell fxFlex="10" *matHeaderCellDef>STT</mat-header-cell>
                                <mat-cell fxFlex="10" *matCellDef="let row;let i=index" data-label="STT">{{i + 1}}</mat-cell>
                            </ng-container>
                            <ng-container matColumnDef="code" style="min-width: 150px;">
                                <mat-header-cell fxFlex="20" *matHeaderCellDef>Số giấy tờ</mat-header-cell>
                                <mat-cell fxFlex="20" *matCellDef="let row;let i=index" data-label="Số giấy tờ">{{ item.MaGiayTo }}</mat-cell>
                            </ng-container>
                            <ng-container matColumnDef="filename">
                                <mat-header-cell *matHeaderCellDef>Tên tập tin</mat-header-cell>
                                <mat-cell *matCellDef="let row;let i=index" data-label="Tên tập tin">
                                    <a href="" (click)="getFileStorage(row.HashIdTapTin, $event);">{{ row.TenTapTin }}</a>
                                </mat-cell>
                            </ng-container>
                            <ng-container matColumnDef="action">
                                <mat-header-cell fxFlex="10" *matHeaderCellDef>Lấy giấy tờ</mat-header-cell>
                                <mat-cell fxFlex="10" *matCellDef="let row;" data-label="Lấy giấy tờ">
                                    <button mat-icon-button (click)="useFileStorage(row.HashIdTapTin, item.MaGiayTo)">
                                        <mat-icon>input</mat-icon>
                                    </button>
                                </mat-cell>
                            </ng-container>
                            <mat-header-row *matHeaderRowDef="getDisplayedColumns(null, 'local')" mat-header-row></mat-header-row>
                            <mat-row *matRowDef="let row; columns: getDisplayedColumns(null, 'local')" mat-row></mat-row>
                        </table>
                    </div>
                </mat-card-content>
            </div>
        </mat-card>
        <mat-card style="margin: 0 20px;" class="formKhoLan" *ngIf="tabSelected == 1">
            <div *ngIf="dataFromNation.length == 0">
                Không có dữ liệu
            </div>
            <div *ngFor="let item of dataFromNation">
                <div class="titleTable">
                    <i>+ {{ item.MaKetQua }}: {{ item.TenGiayTo }}</i>
                </div>
                <mat-card-content class="card-body">
                    <div *ngIf="item.DanhSachTepDinhKem.length == 0" class="mat-elevation-z8">
                        <span>Không có dữ liệu</span>
                    </div>
                    <div *ngIf="item.DanhSachTepDinhKem.length > 0">
                        <table mat-table [dataSource]="item.elements" class="responsive-table mat-elevation-z8">
                            <ng-container matColumnDef="index">
                                <mat-header-cell fxFlex="10" *matHeaderCellDef>STT</mat-header-cell>
                                <mat-cell fxFlex="10" *matCellDef="let row;let i=index" data-label="STT">{{i + 1}}</mat-cell>
                            </ng-container>
                            <ng-container matColumnDef="code" style="min-width: 150px;">
                                <mat-header-cell fxFlex="20" *matHeaderCellDef>Mã kết quả</mat-header-cell>
                                <mat-cell fxFlex="20" *matCellDef="let row;let i=index" data-label="Mã kết quả">{{ item.MaKetQua }}</mat-cell>
                            </ng-container>
                            <ng-container matColumnDef="filename">
                                <mat-header-cell *matHeaderCellDef>Tên tập tin</mat-header-cell>
                                <mat-cell *matCellDef="let row;let i=index" data-label="Tên tập tin">
                                    <a href="" (click)="getFileNation(row.DuongDan, row.TenFile,  $event);">{{ row.TenFile }}</a>
                                </mat-cell>
                            </ng-container>
                            <ng-container matColumnDef="action">
                                <mat-header-cell fxFlex="10" *matHeaderCellDef>Lấy giấy tờ</mat-header-cell>
                                <mat-cell fxFlex="10" *matCellDef="let row;" data-label="Lấy giấy tờ">
                                    <button mat-icon-button (click)="useFileStorageNation(row.DuongDan, row.TenFile, item.MaKetQua)">
                                        <mat-icon>input</mat-icon>
                                    </button>
                                </mat-cell>
                            </ng-container>
                            <mat-header-row *matHeaderRowDef="getDisplayedColumns(null, 'local')" mat-header-row></mat-header-row>
                            <mat-row *matRowDef="let row; columns: getDisplayedColumns(null, 'local')" mat-row></mat-row>
                        </table>
                    </div>
                </mat-card-content>
            </div>
        </mat-card>
    </div>
</div>
