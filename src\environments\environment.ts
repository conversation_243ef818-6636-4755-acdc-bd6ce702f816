export const environment = {
  production: false,
  loadConfigFromUrl: false,
  configUrl: './assets/app.config.json',
  routerConfig: {
    enableTracing: false
  },
  config: {
    keycloakDebug: true,
    keycloakOptions: {
      config: {
        url: 'https://ssotest.vnptigate.vn/auth',
        //url: 'https://xacthuc.kontum.gov.vn/auth',
        //url: 'https://ssodev.vnptigate.vn/auth',
        // url: 'https://digo-oidc.vnptigate.vn/auth',
        // url: 'https://ssohanoi.vnptigate.vn/auth', // hni
        // url: 'https://ssoquangngai.vnptigate.vn/auth', //test
        realm: 'digo',
        clientId: 'web-padsvc'
      },
      initOptions: {
        onLoad: 'check-sso',
        checkLoginIframe: false
      },
      bearerExcludedUrls: ['/sy/app-deployment'],
      loadUserProfileAtStartUp: true
    },
    insufficientPermissionRouterLink: 'error/insufficient-permission',
    apiProviders: {
      digo: {
        rootUrl: 'https://apitest.vnptigate.vn',
        //rootUrl: 'https://apidev.vnptigate.vn',
        // rootUrl: 'https://digo-api.vnptigate.vn',
        // rootUrl: 'https://apihanoi.vnptigate.vn', // hni
        // rootUrl: 'https://apiquangngai.vnptigate.vn/',
        services: {
          basedata: { path: 'ba' },
          basecat: { path: 'bt' },
          fileman: { path: 'fi' },
          human: { path: 'hu' },
          postman: { path: 'po' },
          logman: { path: 'lo' },
          surfeed: { path: 'su' },
          messenger: { path: 'me' },
          padman: { path: 'pa' },
          // padman: { path: '', rootUrl: 'http://localhost:8081' },
          basepad: { path: 'bd' },
          //basepad: { path: '', rootUrl: 'http://localhost:8080' },
          reporter: { path: 're' },
          bpm: { path: 'bpm' },
          modeling: { path: 'modeling-service' },
          adapter: { path: 'integration' },
          //adapter: { path: '', rootUrl: 'http://localhost:8080' },
          chatbotadm: { path: 'botadm' },
          upload: { path: 'upload' },
          storage: { path: 'storage' },
          // storage: { path: '', rootUrl: 'http://localhost:8080' },
          sysman: { path: 'sy' },
          padmanReport: { path: 'pareport' },
        }
      }
    },
    defaultFormIO: {
      eForm: '5f86700730ea36001c3825f9',
      applicantEForm: '5fdc64e6576b78001d53219d'
    },
    onlinePaymentMethodId: '5f7fca9fb80e603d5300dcf5',
    ancestorPlaceId: '5def47c5f47614018c000082',
    tagProvince: '5f9a73e02994dc687e68b593',
    tagDistrict: '5fec06bd1b53d8779bc9ea8b',
    tagWards: '5fec06e61b53d8779bc9ea8c',
    registerURL: 'https://taikhoantest.vnptigate.vn/vi/my-account/sign-up',
    cloudStaticURL: 'https://staticv2.vnptigate.vn',
    webAccountURL: 'https://taikhoantest.vnptigate.vn',
    birtviewerURL: 'https://baocaodongtest.vnptigate.vn/',
    formioURL: 'https://apitest.vnptigate.vn/eform',
    formioDomain: 'https://eformtest.vnptigate.vn/',
    // formioURL: 'https://apihanoi.vnptigate.vn/eform', // hni
    qnaURL: 'https://khaosattest.vnptigate.vn',
    supportFile: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
    deploymentId: '5ee091507d567c9fe29f82fa',
    // deploymentId: '6177ec86c7e34c0c44f3fbf7',
    deploymentIdCloud: '649d3322fdef826cbe68a915',
    // deploymentId: '6119d0769f5aa34c13caba59', // hni
    // deploymentIdCloud: '6119d0769f5aa34c13caba59', // hni
    deploymentUrl: 'https://apiv2.vnptigate.vn/sy',
    appCode: 'web-padsvc',
    subsystemId: '5f7c16069abb62f511880003',
    subsystemWebPadsvcId: '5f7c16069abb62f511880006',
    budgetCodeConfigId: '61c2df9a3957f946770ffb70',
    tutorialLink: 'https://www.youtube.com/embed/3bIGgf224ck?controls=0',
    guideTagId: '5f76a6096f66993d11c88718',
    agencyLevelCategoryId: '5f3a491c4e1bd312a6f00009',
    applyOnlineStepCategoryId: '5f3a491c4e1bd312a6f00029',
    procedureLevelCategoryId: '5f3a491c4e1bd312a6f00008',
    documentTypeCategoryId: '5f3a491c4e1bd312a6f00002',
    implementerCategoryId: '5f5b27fd4e1bd312a6f3ae1f',
    citizenId: '5f3f3c044e1bd312a6f3addf',
    enterpriseId: '5f3f3c2b4e1bd312a6f3ade0',
    districtTagId: '5f8e40db8fffa53e4c073def',
    villageTagId: '5f6b177a4e1bd312a6f3ae4a',
    departmentTagId: '5fc64a9f86940eab2fb01092',
    receivingBallotTagId: '5f7fc83eb80e603d5300dce7',
    procedureLevel1Id: '61f25eb372eb62396f2fed69',
    procedureLevel2Id: '5f5b2c2b4e1bd312a6f3ae23',
    businessRegistrationConfigId: '5fa4bdb432c7c6aed8a795bf',
    dossierReceivingKindCategoryId: '5f3a491c4e1bd312a6f00032',
    receiveResultsByAddress: '5f8969018fffa53e4c073dee',
    placeProvinceTypeId: '5ee304423167922ac55bea01',
    placeDistrictTypeId: '5ee304423167922ac55bea03',
    placeWardTypeId: '5ee304423167922ac55bea03',
    nationId: '5f39f4a95224cf235e134c5c',
    vnpostConfigId: '5f9a335e32c7c6aed8630235',
    vnpostVNPostFeeId: '5fb6300894f06f0a17f05702',
    applicantTypeId: '5f7fc6cfb80e603d5300dce4',
    wardsDefaultId: '5def47c5f47614018c118034',
    paymentMethodCategoryId: '5f3a491c4e1bd312a6f00011',
    webPadsvcSubsystemId: '5f7c16069abb62f511880006',
    agencyMinistryLevelId: '5ff6b1a706d0e31c6bf13e09',
    receiptSupplierCategoryId: '5f3a491c4e1bd312a6f00038',
    subsystemWebOneGateId: '5f7c16069abb62f511880003',
    urlWebChatBot: 'http://localhost:4200/chat',
    // formioURL: 'https://apiquangngai.vnptigate.vn/eform',
    // chatbot
    titleChatBot: 'TỔNG ĐÀI 1022',
    logoAgency: 'assets/img/phone.svg',
    defaultColor: '#ce7a58',
    defaultTitleColor: 'white',
    logoBot: 'assets/img/custumer_sv.svg',
    defaultUserAvatarChatBot: 'assets/img/account_default.svg',
    urlChatBotRasa: 'https://digo-api.vnptigate.vn/bot/webhooks/igate/webhook',
    // Mapbox
    reporterLocation: {
      address: 'Pleiku, Gia Lai, Vietnam',
      latitude: '13.972173',
      longitude: '108.015147'
    },
    mapOption: 'mapbox',
    scale: 'r169:640,r169:320',
    mapbox: {
      accessToken:
        'pk.eyJ1IjoibW5oYWktbWFwYm94IiwiYSI6ImNrYmlvZ3d5bjBmdzcyem5xcWhnaDFrYjIifQ.tdsFG-l6QwZeKXDtO_r99g',
    },
    // end
    chatbotRestartMinute: 60,
    domainFileChatbot: 'https://padsvctest.vncitizens.vn/',
    //
    chatbotConfigure: '1',
    configuratingChatbotId: '606e6764b6c5d41d6b6d30e7',
    languageDefaultId: 228,
    languageDefault: 'vi',
    reloadTimeout: 2000,
    expiredTime: 3000,
    translatePaginator: ['Số dòng', 'Trang đầu', 'Trang trước', 'Trang tiếp theo', 'Trang cuối', 'của'],
    pageSizeOptions: [5, 10, 20, 50],
    searchAfterStopTyping: 800,
    fileUnits: ['bytes', 'KB', 'MB', 'GB', 'TB', 'PB'],
    regValidators: '/[-a-zA-Z0-9@:%_\+.~#?&//=]{2,256}\.[a-z]{2,4}\b(\/[-a-zA-Z0-9@:%_\+.~#?&//=]*)?/gi',
    procedureFormAcceptFileExtension: ['.DOC', '.DOCX', '.PDF', '.XLS', '.XLSX', '.TXT', '.JPG', '.JPEG', '.PNG'],
    procedureFormAcceptFileType: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png'
    ],
    procedureFormMaxFileSize: '5242880',
    rootAgency: {
      id: '5f39fa505224cf235e134c69',
      code: '000.00.00.H58',
      trans: {
        vi: {
          name: 'UBND Tỉnh Tiền Giang'
        },
        en: {
          name: 'People\'s Committee of Tien Giang province'
        }
      },
      provinceId: 82,
    },
    defaultUserAvatar: 'https://staticv2.vnptigate.vn/logo/HCC.png',
    defaultLogo: 'https://staticv2.vnptigate.vn/logo/quoc-huy.png',
    defaultBanner: 'https://staticv2.vnptigate.vn/logo/quoc-huy.png',
    getPublicTokenURL: 'https://ssotest.vnptigate.vn/auth/realms/digo/protocol/openid-connect/token',
    GRANT_TYPE: 'client_credentials',
    CLIENT_ID: 'web-padsvc-public',
    SCOPE: 'openid',
    CLIENT_SECRET: '92b4ad13-3cf6-4380-9116-ec837b0bd482',
    siteTitle: {
      vi: 'Chính quyền số',
      en: 'Digital Government'
    },
    dossierEmailSMSZaloConfig: {
      sms: {
        enable: 1,
        message: {
          vi: 'Nộp hồ sơ thành công! Mã số hồ sơ: {{code}}',
          en: 'Submit successfully! Dossier code: {{code}}'
        },
        characterLimit: 150
      }
    },
    searchMappingDataType: {
      district: '5fc0707a62681a8bef000005',
      province: '5fc0707a62681a8bef000004',
      agency: '5fc0707a62681a8bef000007',
      ward: '5fc0707a62681a8bef000006'
    },
    uiLoaderConfig: {
      bgsColor: '#000000',
      bgsOpacity: 0.5,
      bgsPosition: 'bottom-right',
      bgsSize: 60,
      bgsType: 'ball-spin-clockwise',
      blur: 0,
      delay: 0,
      fastFadeOut: true,
      fgsColor: '#eb6a35',
      fgsPosition: 'center-center',
      fgsSize: 120,
      fgsType: 'ball-scale-multiple',
      gap: 24,
      masterLoaderId: 'master',
      overlayBorderRadius: '0',
      overlayColor: 'rgb(255,255,255)',
      pbColor: 'red',
      pbDirection: 'ltr',
      pbThickness: 3,
      hasProgressBar: false,
      text: '',
      textColor: '#FFFFFF',
      textPosition: 'center-center',
      maxTime: -1,
      minTime: 300
    },
    acceptedPetitionFileType: ['audio/mpeg', 'image/jpeg', 'image/png', 'image/webp', 'image/jpg', 'text/plain', 'application/pdf', 'video/mp4', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    acceptedPetitionFileExtension: '.jpg, .JPG, .png, .PNG, .jpeg, .JPEG, .gif, .GIF, .TIFF, .psd, .PSD, .pdf, .PDF, .EPS, .AI, .INDD, .RAW, .docx, .doc, .dot , .MP4, .mp3',
    dossierTaskStatus: {
      justRegistered: {
        id: '60e409823dfc9609723e493c',
        vi: 'Mới đăng ký',
        en: 'Just registered'
      },
      waitingAdditional: {
        id: '60e409983dfc9609723e493e',
        vi: 'Chờ bổ sung',
        en: 'Waiting additional'
      },
      requestForAdditionalDocuments: {
        id: '60ebf03109cbf91d41f87f8b',
        vi: 'Yêu cầu bổ sung giấy tờ',
        en: 'Request for additional documents'
      },
      dossierAdded: {
        id: '60ebf0db09cbf91d41f87f8c',
        vi: 'Đã bổ sung hồ sơ',
        en: 'Dossier added'
      },
      notAccepted: {
        id: '60ebf14b09cbf91d41f87f8d',
        vi: 'Không được tiếp nhận',
        en: 'Not accepted'
      },
      resultReturned: {
        id: '60ebf17309cbf91d41f87f8e',
        vi: 'Đã trả kết quả',
        en: 'Result returned'
      },
      cancelled: {
        id: '60ed1a5909cbf91d41f87f9f',
        vi: 'Hồ sơ đã hủy',
        en: 'Cancelled'
      },
      pending: {
        id: '60ed1b7c09cbf91d41f87fa0',
        vi: 'Đang tạm dừng',
        en: 'Pending'
      },
      hadResult: {
        id: '60ed1c3409cbf91d41f87fa1',
        vi: 'Có kết quả',
        en: 'Had result'
      }
    },
    dossierMenuTaskRemind: {
      justRegistered: {
        id: '60f52e0d09cbf91d41f88834',
        vi: 'Mới đăng ký',
        en: 'Just registered'
      }
    },
    publicFileUrl: "https://apitest.vnptigate.vn/upload/images",
    igateSubsystemId: '5f7c16069abb62f511880003',
    signPositionDigitalSignature: '10,10,150,70',
    locationDigitalSignature: 'Ký tên',
    messageDisplayDigitalSignature: 'Vui long nhap PIN ky so',
    chatbotExcludeUrls: ['thong-ke-mobile'],
    idPhieuNguoiDan : "634cf3b15f48cc563751654c",
    idPhieuDoanhNghiep: "634cf3ce5f48cc563751654d"
  }
};
