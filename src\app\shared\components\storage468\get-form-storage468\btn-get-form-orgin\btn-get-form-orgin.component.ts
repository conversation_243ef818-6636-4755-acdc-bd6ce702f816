import {Component, EventEmitter, Input, Output} from '@angular/core';
import {GetFormOrginComponent} from "shared/components/storage468/get-form-storage468/get-form-orgin/get-form-orgin.component";
import {IFormOrginInFoOutput} from "shared/components/storage468/storage468.schema";
import {MatDialog} from "@angular/material/dialog";
import {ChooseFormOrginComponent} from "shared/components/storage468/get-form-storage468/choose-form-orgin/choose-form-orgin.component";
import {Storage468Service} from "shared/components/storage468/storage468.service";

@Component({
  selector: 'btn-get-form-orgin',
  templateUrl: './btn-get-form-orgin.component.html',
  styleUrls: ['./btn-get-form-orgin.component.scss']
})
export class BtnGetFormOrginComponent{
  @Output() afterChoose: EventEmitter<object> = new EventEmitter();
  @Input() getFormOrgin:GetFormOrginComponent;
  formOrginId:string;
  @Input() form:any;
  @Input() storage468:any;
  formOrgins?:IFormOrginInFoOutput[] = undefined;

  constructor(private dialog: MatDialog,
              private storage468Service: Storage468Service) {
  }

  findout(){
    if(this.getFormOrgin?.formOrginIds){
      let storageId = this.getFormOrgin?.mapedIds.find(x=>x.igateid===this.form?.code)?.storageid;
      if(storageId===undefined){
        storageId = '';
      }
      const isFindout = this.getFormOrgin?.formOrginIds.includes(storageId) && this.getFormOrgin?.found===true;
      return isFindout;
    }
    return false;
  }

  openChooseDiglog(){
    this.formOrginId = this.getFormOrgin?.mapedIds.find(i=>i.igateid===this.form?.code)?.storageid;
    if(!this.formOrgins){
      this.formOrgins = this.getFormOrgin.forms.filter(i=>i.formOrgin.id===this.formOrginId);
    }

    if(this.formOrgins.length > 1){
      const name = this.form?.name;
      const dialogRef = this.dialog.open(ChooseFormOrginComponent, {
        width: 'auto',
        data: {
          formOrginInfos: this.formOrgins,
          title: `Danh sách: ${name}`
        },
        disableClose: true,
        autoFocus: false
      });

      dialogRef.afterClosed().subscribe(files=>{
        if(files){
          const fileIds = [].concat(files).map(i=>i.id);
          this.storage468Service.asyncStorageToFileman(fileIds).subscribe(filemans=>{
            this.afterChoose.emit({files: filemans, formOrginId: this.formOrginId, formId: this.form?.id});
          });
        }
      });
    }else {
      const fileIds = this.formOrgins[0].files.map(i=>i.id);
      this.storage468Service.asyncStorageToFileman(fileIds).subscribe(filemans=>{
        this.afterChoose.emit({files: filemans, formOrginId: this.formOrginId, formId: this.form?.id});
      });
    }
  }
}
