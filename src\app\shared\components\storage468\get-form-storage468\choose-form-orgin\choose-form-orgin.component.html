<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>{{data.title}}</h3>
<table mat-table [dataSource]="data.formOrginInfos" class="mat-elevation-z8">
  <ng-container matColumnDef="code">
    <th mat-header-cell *matHeaderCellDef>Mã giấy tờ</th>
    <td mat-cell *matCellDef="let element"> {{element.identityDoc}} </td>
  </ng-container>
  <ng-container matColumnDef="createDate">
    <th mat-header-cell *matHeaderCellDef> Ngày tạo</th>
    <td mat-cell *matCellDef="let element"> {{element.createDate| date: 'dd/MM/yyyy'}} </td>
  </ng-container>
  <ng-container matColumnDef="files">
    <th mat-header-cell *matHeaderCellDef>Tập tin</th>
    <td mat-cell *matCellDef="let element">
      <button *ngFor="let file of element.files" mat-button fxFlex='100' class="btn_upload">
        <mat-icon>attachment</mat-icon>
        <span class="text">{{file?.filename}}</span>
      </button>
    </td>
  </ng-container>
  <ng-container matColumnDef="action">
    <th mat-header-cell *matHeaderCellDef> Chọn </th>
    <td mat-cell *matCellDef="let element">
      <button mat-icon-button class="btn_acction" (click)="dialogRef.close(element?.files)" title="Sử dụng giấy tờ này">
        <mat-icon>output</mat-icon>
      </button>
    </td>
  </ng-container>
  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
</table>
