import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, OnInit, Inject } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { EnvService } from 'src/app/core/service/env.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ViewProcessDiagramComponent, ViewProcessDiagramDialogModel } from 'src/app/shared/components/view-process-diagram/view-process-diagram.component';
import * as xml2js from 'xml2js';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'app-process-handle',
  templateUrl: './process-handle.component.html',
  styleUrls: ['./process-handle.component.scss']
})
export class ProcessHandleComponent implements OnInit {

  config = this.envService.getConfig();
  dossierId: string;
  dossierCode: string;
  dossierDetail: any;
  timesheetId: any;
  tasks = [];
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  processName = '';
  isDisplayStep=false;
  bpmProcessDefinitionId = '';
  checkOneGateAdminMaster = Number(sessionStorage.getItem('oneGateAdminMaster')) || 0;
  checkOneGateProcessCatalog = Number(sessionStorage.getItem('oneGateProcessCatalog')) || 0;
  env = this.deploymentService.getAppDeployment()?.env;
  // Timesheet
  timesheet = {
    receivingDue: new Date(),
    dateDiff: '',
    endDate: new Date(),
    isOverDue: false,
    isTaskOverDue: false,
    dueTimer: {
      day: 0,
      hour: 0,
      minute: 0,
      second: 0
    },
    taskDueTimer: {
      day: 0,
      hour: 0,
      minute: 0,
      second: 0
    }
  };

  diagramUrl: any;
  taskId: any = [];

  //Cấu hình đổi tên xem tiến trình
  viewProcessButtonName = this.env?.OS_HCM?.viewProcessButtonName ? this.env?.OS_HCM?.viewProcessButtonName : null;
  processButtonName = null;
  isShowHGI = this.deploymentService?.env?.OS_HGI?.isShowHGI == true?true:false;
  statusName = "";

  dossierTaskStatusNew = false;
  showAppointmentDateProcess = this.deploymentService.env?.OS_HCM?.showAppointmentDateProcess;
  showEndDateProcess = this.deploymentService.env?.OS_HCM?.showEndDateProcess;
  endData = '';
  //Qni
  detailProcessByActivitiIdQni = this.deploymentService.env?.OS_QNI?.detailProcessByActivitiIdQni;
  //Hcm
  detailProcessByActivitiIdHcm= this.deploymentService.env?.OS_HCM?.detailProcessByActivitiIdHcm;
  ShowTheNewInterfaceDVCTP = this.deploymentService?.getAppDeployment()?.ShowTheNewInterfaceDVCTP;
  configViewProcessingKHA: boolean = this.deploymentService?.getAppDeployment()?.configViewProcessingKHA || false;
  configViewStepByStepProcessKHA: boolean = this.deploymentService?.getAppDeployment()?.configViewStepByStepProcessKHA || false;
  processSteps = [];
  constructor(
    private http: HttpClient,
    private keycloak: KeycloakService,
    public dossierService: DossierService,
    public sanitizer: DomSanitizer,
    private dialog: MatDialog,
    private processService: ProcessService,
    private procedureService: ProcedureService,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private router: Router,
    public dialogRef: MatDialogRef<ProcessHandleComponent>,
    private deploymentService: DeploymentService,
    @Inject(MAT_DIALOG_DATA) public data: ProcessHandleDialogModel
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
  }

  ngOnInit(): void {
    this.getDossierDetail();
    if(!!this.env?.OS_KTM?.isDisplayStep){
      this.isDisplayStep = this.env?.OS_KTM?.isDisplayStep;
    }
    //Lấy tên nút xem qy trình
    if(this.viewProcessButtonName)
    {
      this.processButtonName = this.viewProcessButtonName[this.selectedLang];
      if(this.processButtonName)
      {
        this.processButtonName = this.processButtonName.replace('Xem ', '').replace('View ', '');
        if(this.processButtonName.length > 1)
          this.processButtonName = this.processButtonName.substring(0,1).toUpperCase() + this.processButtonName.substring(1,this.processButtonName.length);
      }
    }
  }

  getProcessSteps(processDefinitionId) {
    this.processService.getProcessDefinitionTaskByAcitivitiId(processDefinitionId).subscribe(
      (data) => {
        this.processSteps = data.content || data;
        // Format time units for display
        this.processSteps.forEach(step => {
          if (step.processingTimeUnit === 'd') {
            step.formattedTimeUnit = 'ngày';
          } else if (step.processingTimeUnit === 'h') {
            step.formattedTimeUnit = 'giờ';
          } else if (step.processingTimeUnit === 'm') {
            step.formattedTimeUnit = 'phút';
          } else {
            step.formattedTimeUnit = step.processingTimeUnit;
          }
        });
      },
      (error) => {
        console.error('Error fetching process steps:', error);
      }
    );
  }

  onConfirm() {
    this.dialogRef.close(true);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  getDossierDetail() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;

      if((this.dossierDetail?.dossierStatus?.id == 0 || this.dossierDetail?.dossierStatus?.id == 1) && !this.dossierDetail?.task)
        this.dossierTaskStatusNew = true;

      this.getEndDate(this.dossierDetail);

      // tslint:disable-next-line: max-line-length
      this.dossierDetail.dossierReceivingKind.name = this.getTranslateName(this.dossierDetail.dossierReceivingKind.name, this.selectedLangId);
      this.dossierDetail.dossierStatus.name = this.getTranslateName(this.dossierDetail.dossierStatus.name, this.selectedLangId);
      let processingTime = 0;
      if (!!this.dossierDetail.processingTime && this.dossierDetail.processingTime.length !== 0) {
        switch (this.dossierDetail.processingTimeUnit) {
          case 'y':
            processingTime = Number(this.dossierDetail.processingTime) * 365;
            break;
          case 'M':
            processingTime = Number(this.dossierDetail.processingTime) * 30;
            break;
          case 'd':
            processingTime = Number(this.dossierDetail.processingTime);
            break;
          case 'H:m:s':
            processingTime = Number(this.dossierDetail.processingTime) / 24;
            break;
          case 'h':
            processingTime = Number(this.dossierDetail.processingTime) / 24;
            break;
          case 'm':
            processingTime = Number(this.dossierDetail.processingTime) / (24 * 60);
            break;
        }
      }
      let requestBodyObj = [];
      if (this.env.limitedAppointmentTime) {
        requestBodyObj = [
          {
            timesheet: {
              id: this.dossierDetail.procedureProcessDefinition?.processDefinition?.timesheet?.id
            },
            dossier: {
              id: this.dossierId
            },
            duration: processingTime,
            startDate: this.dossierDetail.acceptedDate,
            endDate: '',
            checkOffDay: true,
            offTime: this.env.limitedAppointmentTime,
            processingTimeUnit: this.dossierDetail.processingTimeUnit
          }
        ];
      } else if (this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure) {
        requestBodyObj = [
          {
            timesheet: {
              id: this.dossierDetail.procedureProcessDefinition?.processDefinition?.timesheet?.id
            },
            dossier: {
              id: this.dossierId
            },
            duration: processingTime,
            startDate: this.dossierDetail.acceptedDate,
            endDate: '',
            checkOffDay: true,
            extendHCM: {
              offTime: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure,
              startAtNextDay: this.dossierDetail?.extendHCM?.startAtNextDay ?? false,
              appointmentAtNextDay: this.dossierDetail?.extendHCM?.appointmentAtNextDay ?? false
            },
            processingTimeUnit: this.dossierDetail.processingTimeUnit
          }
        ];
      } else {
        requestBodyObj = [
          {
            timesheet: {
              id: this.dossierDetail.procedureProcessDefinition?.processDefinition?.timesheet?.id
            },
            dossier: {
              id: this.dossierId
            },
            duration: processingTime,
            startDate: this.dossierDetail.acceptedDate,
            endDate: '',
            checkOffDay: true,
            processingTimeUnit: this.dossierDetail.processingTimeUnit
          }
        ];
      }
      const requestBody = JSON.stringify(requestBodyObj, null, 2);
      this.dossierService.postTimesheet(requestBody).subscribe(res => {
        this.timesheet.endDate = res[0].due;
      }, err => {
        console.log(err);
      });
      if (this.configViewStepByStepProcessKHA) {
        this.getProcessSteps(this.dossierDetail.task[0].processDefinition.id);
      }
      if (this.detailProcessByActivitiIdQni)
      {
        this.processService.getDetailProcessByActivitiIdQni(this.dossierDetail.task[0].processDefinition.id).subscribe(async result => {
          this.timesheetId = result.timesheet.id;
          this.processName = result.name;
          this.bpmProcessDefinitionId = result.id;

          const listTask = await this.processService.getListTaskByProcessDefId(result.id).toPromise();
          const activitiModelId = result.activiti.model.id;

          if (!!data.activitiProcessInstance) {
            this.getProcessInstances(data.activitiProcessInstance.id, listTask.content, activitiModelId);
          }
      });
      } else if (this.detailProcessByActivitiIdHcm){
          this.processService.getDetailProcessByActivitiIdHcm(this.dossierDetail.task[0].processDefinition.id).subscribe(async result => {
            this.timesheetId = result.timesheet.id;
            this.processName = result.name;
            this.bpmProcessDefinitionId = result.id;

            const listTask = await this.processService.getListTaskByProcessDefId(result.id).toPromise();
            const activitiModelId = result.activiti.model.id;

            if (!!data.activitiProcessInstance) {
              this.getProcessInstances(data.activitiProcessInstance.id, listTask.content, activitiModelId);
            }
          });
      }else{
        this.processService.getDetailProcessByActivitiId(this.dossierDetail.task[0].processDefinition.id).subscribe(async result => {
          this.timesheetId = result.timesheet.id;
          this.processName = result.name;
          this.bpmProcessDefinitionId = result.id;

          const listTask = await this.processService.getListTaskByProcessDefId(result.id).toPromise();
          const activitiModelId = result.activiti.model.id;

          if (!!data.activitiProcessInstance) {
            this.getProcessInstances(data.activitiProcessInstance.id, listTask.content, activitiModelId);
          }
        });
      }
      
  })
  }

  getEndDate(data)
  {
    this.endData = '';
    switch (data.processingTimeUnit) {
      case 'y':
        this.endData = data.processingTime + ' năm';
        break;
      case 'm':
        this.endData = data.processingTime + ' tháng';
        break;
      case 'd':
        this.endData = data.processingTime + ' ngày';
        break;
      case 'H:m:s':
        this.endData = data.processingTime + ' giờ';
        break;
    }
  }

  getTranslateName(nameArr: any[], languageId: number) {
    const index = nameArr.findIndex(item => item.languageId = languageId);
    if (index > -1) {
      return nameArr[index].name;
    }
    return '';
  }

  getProcessInstances(processId, listTask, activitiModelId) {
    this.dossierService.getProcessInstances(processId).subscribe(async data => {

    //   const xmlString = await data.text();
    //   const parser = new xml2js.Parser({ strict: false, trim: true });
    //   parser.parseString(xmlString, (err, result) => {
    //     this.xml = result;
    //   });

    //   for await (const g of this.xml.SVG.G[0].G) {
    //     if (g.$.ID) {
    //       const taskData = listTask.find(item => item.activiti.definitionKey === g.$.ID);
    //       if (!!taskData) {
    //         this.tasks.push(taskData);
    //       }
    //     }
    //   }

    //   for await (const task of this.dossierDetail.task) {
    // tslint:disable-next-line: max-line-length
    //     const index = this.tasks.findIndex(item => item.activiti.definitionKey === task.bpmProcessDefinitionTask.definitionTask.definitionKey);
    //     if (index !== -1) {
    //       this.tasks[index].assignee = task.assignee;
    //       this.tasks[index].activitiTask = task.activitiTask;
    //       this.tasks[index].candidateGroup = task.candidateGroup;
    //       this.tasks[index].createdDate = task.createdDate;
    //       this.tasks[index].updatedDate = task.updatedDate;
    //     }
    //   }

    //   let sum = 0;
    //   const requestBodyTimesheet = [];

    //   for await (const task of this.tasks) {
    //     if (!!task.candidateGroup && task.candidateGroup.length !== 0) {
    //       for await (const name of task.candidateGroup[0].name) {
    //         if (name.languageId === this.selectedLangId) {
    //           task.candidateGroupName = name.name;
    //         }
    //       }
    //     }

    //     sum = sum + this.convertTime2Date(task.processingTime, task.processingTimeUnit);
    //     const timesheetElement = {
    //       timesheet: {
    //         id: this.timesheetId
    //       },
    //       dossier: {
    //         id: this.dossierId
    //       },
    //       duration: sum,
    //       startDate: this.tasks[0].createdDate,
    //       endDate: null,
    //       checkOffDay: true
    //     };
    //     requestBodyTimesheet.push(timesheetElement);
    //   }

    //   this.dossierService.postTimesheet(requestBodyTimesheet).subscribe(result => {
    //     for (let i = 0; i < this.tasks.length; i++) {
    //       this.tasks[i].dueDate = result[i].due;
    //     }
    //   });

    //   this.diagramUrl = this.procedureService.getUrlModel(activitiModelId);
    //   for (const task of this.dossierDetail.task) {
    //     if (task.isCurrent === 1) {
    //       this.taskId.push(task.bpmProcessDefinitionTask.definitionTask.definitionKey);
    //     }
    //   }
    // }
    // , err => {
    //   switch (err.status) {
    //     case 406: {
    //       const msgObj = {
    //         vi: 'Quy trình hồ sơ: ' + this.dossierCode,
    //         en: `Dossier's process: ` + this.dossierCode
    //       };
    //       this.viewProcess(activitiModelId, msgObj[this.selectedLang], '');
    //       break;
    //     }
    //     case 404: {
    //       const msgObj = {
    //         vi: 'Không tìm thấy quy trình!',
    //         en: 'The process could not be found!'
    //       };
    //       this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    //       break;
    //     }
    //     default: {
    //       const msgObj = {
    //         vi: 'Một lỗi không rõ đã xảy ra!',
    //         en: 'An unknown error has occurred!'
    //       };
    //       this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    //       break;
    //     }
    //   }
    //   this.dialogRef.close();
    // });
    })
    const token = this.keycloak.getKeycloakInstance().token;
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    if (this.detailProcessByActivitiIdQni)
    {
      this.diagramUrl = this.procedureService.getUrlModelQni(activitiModelId);
    }
    else
    {
      this.diagramUrl = this.procedureService.getUrlModel(activitiModelId);
    }
    if (this.detailProcessByActivitiIdHcm)
    {
      this.diagramUrl = this.procedureService.getUrlModelHcm(activitiModelId);
    }
    else
    {
      this.diagramUrl = this.procedureService.getUrlModel(activitiModelId);
    }

    this.http.get(this.diagramUrl, { headers, responseType: 'text' }).subscribe(data => {
      const xmlString = data;
      let xml: any = {};
      const parser = new xml2js.Parser({ strict: false, trim: true });
      parser.parseString(xmlString, async (err, result) => {
        xml = result;

        const INCLUSIVEGATEWAY = xml['BPMN2:DEFINITIONS']['BPMN2:PROCESS'][0]['BPMN2:INCLUSIVEGATEWAY'] || [];
        const EXCLUSIVEGATEWAY = xml['BPMN2:DEFINITIONS']['BPMN2:PROCESS'][0]['BPMN2:EXCLUSIVEGATEWAY'] || [];
        const PARALLELGATEWAY = xml['BPMN2:DEFINITIONS']['BPMN2:PROCESS'][0]['BPMN2:PARALLELGATEWAY'] || [];
        const USERTASK = xml['BPMN2:DEFINITIONS']['BPMN2:PROCESS'][0]['BPMN2:USERTASK'];

        const INCLUSIVEGATEWAYS = this.addGateway(INCLUSIVEGATEWAY);
        const EXCLUSIVEGATEWAYS = this.addGateway(EXCLUSIVEGATEWAY);
        const PARALLELGATEWAYS = this.addGateway(PARALLELGATEWAY);
        const USERTASKS = [];

        for (const userTaskEle of USERTASK) {
          const userTaskObj = {
            id: userTaskEle.$.ID,
            incoming: userTaskEle['BPMN2:INCOMING'],
            gatewayId: []
          };

          for (let i = 0, length = userTaskEle['BPMN2:INCOMING'].length; i < length; i++) {
            const index = INCLUSIVEGATEWAYS.findIndex(itemm => itemm.outgoing === userTaskEle['BPMN2:INCOMING'][i]);
            if (index > -1) {
              if (INCLUSIVEGATEWAYS.findIndex(itemm => itemm.incoming === userTaskEle['BPMN2:OUTGOING'][i]) < 0) {
                userTaskObj.gatewayId.push(INCLUSIVEGATEWAYS[index].id);
              }
            }

            const index1 = EXCLUSIVEGATEWAYS.findIndex(itemm => itemm.outgoing === userTaskEle['BPMN2:INCOMING'][i]);
            if (index1 > -1) {
              if (EXCLUSIVEGATEWAYS.findIndex(itemm => itemm.incoming === userTaskEle['BPMN2:OUTGOING'][i]) < 0) {
                userTaskObj.gatewayId.push(EXCLUSIVEGATEWAYS[index1].id);
              }
            }

            const index2 = PARALLELGATEWAYS.findIndex(itemm => itemm.outgoing === userTaskEle['BPMN2:INCOMING'][i]);
            if (index2 > -1) {
              if (PARALLELGATEWAYS.findIndex(itemm => itemm.incoming === userTaskEle['BPMN2:OUTGOING'][i]) < 0) {
                userTaskObj.gatewayId.push(PARALLELGATEWAYS[index2].id);
              }
            }
          }

          USERTASKS.push(userTaskObj);
        }

        // for await (const g of USERTASKS) {
        //   const taskData = listTask.find(item => item.activiti.definitionKey === g.id);
        //   taskData.gatewayId = g.gatewayId;
        //   taskData.taskId = g.id;
        //   taskData.incoming = g.incoming;

        //   if (!!taskData) {
        //     this.tasks.push(taskData);
        //   }
        // }

        for await (const task of this.dossierDetail.task) {
          // tslint:disable-next-line: max-line-length
          // const index = this.tasks.findIndex(item => item.activiti.definitionKey === task.bpmProcessDefinitionTask.definitionTask.definitionKey);
          // if (index !== -1) {
          //   this.tasks[index].assignee = task.assignee;
          //   this.tasks[index].activitiTask = task.activitiTask;
          //   this.tasks[index].candidateGroup = task.candidateGroup;
          //   this.tasks[index].createdDate = task.createdDate;
          //   this.tasks[index].updatedDate = task.updatedDate;
          // }

          // tslint:disable-next-line: max-line-length
          // const taskData = listTask.find(item => item.activiti.definitionKey === task.bpmProcessDefinitionTask.definitionTask.definitionKey);
          // taskData.assignee = task.assignee;
          // taskData.activitiTask = task.activitiTask;
          // taskData.candidateGroup = task.candidateGroup;
          // taskData.createdDate = task.createdDate;
          // taskData.updatedDate = task.updatedDate;

          // const userTask = USERTASKS.find(item => item.id === task.bpmProcessDefinitionTask.definitionTask.definitionKey);
          // taskData.gatewayId = userTask.gatewayId;
          // taskData.taskId = userTask.id;
          // taskData.incoming = userTask.incoming;
          this.tasks.push(task);
        }

        for await (const task of this.tasks) {
          if (!!task.agency && !!task.agency.name) {
            for await (const name of task.agency.name) {
              if (name.languageId === this.selectedLangId) {
                task.agencyName = name.name;
              }
            }
            if (!!task.agency.parent && !!task.agency.parent.name) {
              for await (const name of task.agency.parent.name) {
                if (name.languageId === this.selectedLangId) {
                  task.agencyName += ' - ' + name.name;
                }
              }
            }
          }
        }

        // let sum = 0;
        // const requestBodyTimesheet = [];
        // console.log(this.tasks);
        // if (this.tasks.length > 0) {
        //   // tslint:disable-next-line: max-line-length
        //   sum = sum + this.convertTime2Date(this.tasks[0].bpmProcessDefinitionTask.processingTime, this.tasks[0].bpmProcessDefinitionTask.processingTimeUnit);
        //   let timesheetElement = {};
        //   if (this.env.limitedAppointmentTime) {
        //     timesheetElement = {
        //       timesheet: {
        //         id: this.timesheetId
        //       },
        //       dossier: {
        //         id: this.dossierId
        //       },
        //       duration: sum,
        //       startDate: this.tasks[0].createdDate,
        //       endDate: null,
        //       checkOffDay: true,
        //       offTime: this.env.limitedAppointmentTime,
        //       processingTimeUnit: this.tasks[0].bpmProcessDefinitionTask.processingTimeUnit
        //     };
        //   } else {
        //     timesheetElement = {
        //       timesheet: {
        //         id: this.timesheetId
        //       },
        //       dossier: {
        //         id: this.dossierId,
        //         code:this.dossierCode

        //       },
        //       duration: sum,
        //       startDate: this.tasks[0].createdDate,
        //       endDate: null,
        //       checkOffDay: true,
        //       processingTimeUnit: this.tasks[0].bpmProcessDefinitionTask.processingTimeUnit
        //     };
        //   }
        //   requestBodyTimesheet.push(timesheetElement);
        // }

        // for (let i = 1, length = this.tasks.length; i < length; i++) {
        //   sum = 0;
        //   // if (this.arraysContainSame(this.tasks[i].gatewayId, this.tasks[i - 1].gatewayId)) {
        // //     let timesheetElement = {};
        // //     if (this.env.limitedAppointmentTime) {
        // //       timesheetElement = {
        // //         timesheet: {
        // //           id: this.timesheetId
        // //         },
        // //         dossier: {
        // //           id: this.dossierId
        // //         },
        // //         duration: sum,
        // //         startDate: this.tasks[0].createdDate,
        // //         endDate: null,
        // //         checkOffDay: true,
        // //         offTime: this.env.limitedAppointmentTime,
        // //         processingTimeUnit: this.tasks[i - 1].gatewayId
        // //       };
        // //     } else {
        // //       timesheetElement = {
        // //         timesheet: {
        // //           id: this.timesheetId
        // //         },
        // //         dossier: {
        // //           id: this.dossierId
        // //         },
        // //         duration: sum,
        // //         startDate: this.tasks[0].createdDate,
        // //         endDate: null,
        // //         checkOffDay: true,
        // //         processingTimeUnit: this.tasks[i - 1].gatewayId
        // //       };
        // //     }
        // //     requestBodyTimesheet.push(timesheetElement);
        //   // } else {
        //   // tslint:disable-next-line: max-line-length
        //   sum = sum + this.convertTime2Date(this.tasks[i].bpmProcessDefinitionTask.processingTime, this.tasks[i].bpmProcessDefinitionTask.processingTimeUnit);
        //   let timesheetElement = {};
        //   if (this.env.limitedAppointmentTime) {
        //     timesheetElement = {
        //       timesheet: {
        //         id: this.timesheetId
        //       },
        //       dossier: {
        //         id: this.dossierId
        //       },
        //       duration: sum,
        //       startDate: this.tasks[i].createdDate,
        //       endDate: null,
        //       checkOffDay: true,
        //       offTime: this.env.limitedAppointmentTime,
        //       processingTimeUnit: this.tasks[i].bpmProcessDefinitionTask.processingTimeUnit
        //     };
        //   } else {
        //     timesheetElement = {
        //       timesheet: {
        //         id: this.timesheetId
        //       },
        //       dossier: {
        //         id: this.dossierId
        //       },
        //       duration: sum,
        //       startDate: this.tasks[i].createdDate,
        //       endDate: null,
        //       checkOffDay: true,
        //       processingTimeUnit: this.tasks[i].bpmProcessDefinitionTask.processingTimeUnit
        //     };
        //   }
        //   requestBodyTimesheet.push(timesheetElement);
        // }
        // // }

        // console.log(requestBodyTimesheet);

        // tslint:disable-next-line: no-shadowed-variable
        for (const task of this.dossierDetail.task) {
          if (task.isCurrent === 1) {
            this.taskId.push(task.bpmProcessDefinitionTask.definitionTask.definitionKey);
          }
        }
      });

    });
  }

  arraysContainSame(a, b) {
    // a = Array.isArray(a) ? a : [];
    // b = Array.isArray(b) ? b : [];
    if (a.length === 0 || b.length === 0) {
      return false;
    }
    return a.every(el => b.includes(el)) || b.every(el => a.includes(el));
  }

  addGateway(GATEWAY) {
    const GATEWAYS = [];
    for (let i = 0, length = GATEWAY.length; i < length; i++) {
      for (let j = 0, lengthj = GATEWAY[i]['BPMN2:OUTGOING'].length; j < lengthj; j++) {
        const obj = {
          id: GATEWAY[i].$.ID,
          outgoing: GATEWAY[i]['BPMN2:OUTGOING'][j],
          incoming: GATEWAY[i]['BPMN2:INCOMING'][j]
        };

        GATEWAYS.push(obj);
      }
    }

    return GATEWAYS;
  }

  convertTime2Date(processingTime, processingTimeUnit) {
    switch (processingTimeUnit) {
      case 'd':
        return Number(processingTime);
      case 'h':
        return Number(processingTime) / 24;
      case 'm':
        return Number(processingTime) / 1440;
    }
  }

  viewProcess(activitiId, name, bpmProcessDefinitionId) {
    const dialogData = new ViewProcessDiagramDialogModel(activitiId, name, bpmProcessDefinitionId);
    const dialogRef = this.dialog.open(ViewProcessDiagramComponent, {
      width: '80vw',
      height: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  processConfig() {
    this.router.navigate(['/onegate-data/list-process/config/' + this.bpmProcessDefinitionId]);
    this.dialogRef.close();
  }

  getNameDossierStatusHGI(name){
      let rs = '';
      if (name){
          if (Array.isArray(name)){
              name.forEach(item =>{
                  if ( item.languageId === this.selectedLangId){
                      rs = item.name;
                  }
              });
              return rs;
          }
          return name;
      }
      return rs;
  }
}

export class ProcessHandleDialogModel {
  constructor(public dossierId: string, public dossierCode: string) { }
}
