import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import { FormGroup, FormControl } from '@angular/forms';
import {IFileInfo, IFormOrginInFoInput, IFormOrginInFoOutput, IFormOrginSave} from "shared/components/storage468/storage468.schema";
import {FormOrginService} from "data/service/form-orgin/form-orgin.service";
import { Storage468Service } from '../../storage468.service';
import {SnackbarService} from "data/service/snackbar/snackbar.service";
import {EnvService} from "core/service/env.service";
import { KeycloakService } from 'keycloak-angular';

@Component({
  selector: 'app-save2storage',
  templateUrl: './save-form-orgin.component.html',
  styleUrls: ['./save-form-orgin.component.scss']
})
export class SaveFormOrginComponent implements OnInit{
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  displayedColumns: string[] = ['stt', 'filename','action'];
  saveForm = new FormGroup({
    identityDoc: new FormControl(this.data.docIdentityName?'':this.data.formOrginInfo.identityNumber),
    fullname: new FormControl(this.data?.formOrginInfo?.fullname),
    identityNumber: new FormControl(this.data?.formOrginInfo?.identityNumber),
    birthday: new FormControl(this.data?.formOrginInfo?.birthday),
    files: new FormControl(''),
    expireDay: new FormControl(''),
    effectiveDay: new FormControl(''),
    txtEffectiveDay: new FormControl(''),
    cboTypeTime: new FormControl('')
  });
  dataSource?:IFileInfo[] = [];
  selectedFileIds:string[] = [this.data.formOrginInfo.fileId];
  infoWarningMsg?:string;

  constructor(public dialogRef: MatDialogRef<SaveFormOrginComponent>,
              private storage468Service: Storage468Service,
              private envService: EnvService,
              private snackbarService: SnackbarService,
              private keycloakService: KeycloakService,
              private formOrginService: FormOrginService,
              @Inject(MAT_DIALOG_DATA) public data: {
                formOrginInfo?: IFormOrginInFoInput,
                title: string,
                type?: number,
                docIdentityName?: string,
                expire?: boolean}) {}

  ngOnInit(): void {
    this.dataSource = this.data?.formOrginInfo?.files;
    if(this.data?.formOrginInfo?.oldIdentityDoc){
      this.infoWarningMsg = `Giấy tờ ${this.data?.formOrginInfo?.oldIdentityDoc} đã tồn tại, nếu lưu sẽ thực hiện ghi đè`;
    }
    console.log();
  }

  getDDMMYYYY(isoDate){
    if(isoDate == '' || isoDate == null) return '';
    if(isoDate == '') return '';
    let date = new Date(isoDate);
    let year = date.getFullYear();
    let month = date.getMonth()+1;
    let day = date.getDate();
    let mm = `${month}`;
    let dd = `${day}`

    if (day < 10) {
      dd = `0${day}`
    }
    if (month < 10) {
      mm = `0${month}`;
    }
    return `${dd}/${mm}/${year}`;
  }

  pushOrPop(id:string){
    if(this.selectedFileIds.includes(id)){
      this.selectedFileIds = this.selectedFileIds.filter(i=>i !== id);
    }else {
      this.selectedFileIds.push(id);
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

  changeExpireDay(){
    this.saveForm.controls['effectiveDay'].setValue('');
    this.saveForm.controls['txtEffectiveDay'].setValue('');
    this.saveForm.controls['cboTypeTime'].setValue('');
  }

  clearExpireDay(){
    this.saveForm.controls['expireDay'].setValue('');
  }

  checkValidAboutTime(expireDay, effectiveDay,txtEffectiveDay,cboTypeTime){
    if(this.data?.formOrginInfo?.expire != true) return '';
    let ErrorMsg = '';
    if(expireDay == '' && effectiveDay == ''){
      ErrorMsg = 'Vui lòng chọn ngày hết hạn hoặc ngày hiệu lực !';
      return ErrorMsg;
    }
    if(effectiveDay == '') {
      ErrorMsg = 'Vui lòng chọn ngày hiệu lực !';
    } else if (txtEffectiveDay == '') {
      ErrorMsg = 'Vui lòng nhập thời gian hiệu lực !';
    } else if (cboTypeTime == '') {
      ErrorMsg = 'Vui lòng chọn đơn vị thời gian';
    }
    if((expireDay != '') || (effectiveDay != '' && txtEffectiveDay != '' && cboTypeTime != '')){
      ErrorMsg = '';
      return ErrorMsg;
    }
    return ErrorMsg;
  }

  onSave(){
    let expireDay = this.getDDMMYYYY(this.saveForm.controls['expireDay'].value);
    let effectiveDay = this.getDDMMYYYY(this.saveForm.controls['effectiveDay'].value);
    let txtEffectiveDay = this.saveForm.controls['txtEffectiveDay'].value;
    let cboTypeTime = this.saveForm.controls['cboTypeTime'].value;
    let errorMsg = this.checkValidAboutTime(expireDay, effectiveDay, txtEffectiveDay, cboTypeTime);
    if(errorMsg != '') {
      this.infoWarningMsg = errorMsg;
      return;
    }
    const file = this.data.formOrginInfo.files.filter(i=>i.id===this.data.formOrginInfo.fileId)[0];
    this.storage468Service.uploadFileToStorage(this.data.formOrginInfo.identityNumber,
                                               this.data.formOrginInfo.formOrginId,
                                                file.id,file.filename).subscribe(async rs=>{
    const fileStorage = rs.id;
    let aboutTime = txtEffectiveDay + cboTypeTime;
    let identityNumber, fullname, birthday = '';
    await this.keycloakService.loadUserProfile().then(user =>{   
        identityNumber = user['attributes'].identity_number[0];
        fullname = user['attributes'].fullname[0];
        birthday = user['attributes'].birthday[0];
    });

    let userInfo = {
      identityNumber: identityNumber,
      fullname: fullname,
      birthday: birthday
    };

    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let userAgencyInfo = {
      code: userAgency.code,
      name: userAgency.name
    };
      const saveInput:IFormOrginSave = {
        files:[fileStorage],
        formOrginId: this.data.formOrginInfo.formOrginId,
        birthday: this.data.formOrginInfo.birthday,
        fullname: this.data.formOrginInfo.fullname,
        identityDoc:  this.saveForm.controls['identityDoc'].value,
        identityNumber: this.data.formOrginInfo.identityNumber,
        expiryDate: expireDay,
        applyDate: effectiveDay,
        aboutTime: aboutTime,
        createUser: userInfo,
        agency: userAgencyInfo
      };

      this.storage468Service.saveToStorage(saveInput).subscribe(rs=>{
        const msgObj = {
          vi: 'Giấy tờ đã được lưu vào kho',
          en: `Save form orgin is success`,
        };
        this.snackbarService.openSnackBar(1, '', msgObj[this.selectedLang], 'success_notification', this.config.expiredTime);
        this.dialogRef.close();
      }, err=>{
        console.log('onSave--' + err.error.message);
      });
    });
  }
}
