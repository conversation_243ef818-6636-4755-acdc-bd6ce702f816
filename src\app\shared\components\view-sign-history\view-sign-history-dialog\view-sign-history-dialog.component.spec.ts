import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ViewSignHistoryDialogComponent } from './view-sign-history-dialog.component';

describe('ViewSignHistoryDialogComponent', () => {
  let component: ViewSignHistoryDialogComponent;
  let fixture: ComponentFixture<ViewSignHistoryDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ViewSignHistoryDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ViewSignHistoryDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
