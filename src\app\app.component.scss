/*==============================================================Form*/
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #44444450;
  }
  
  .input-half-width {
    width: 49%;
  }
  
  .input-1-3-width {
    width: 32.3%;
  }
  
  .input-full-width {
    width: 100%;
  }
  
  .input-1-4-width {
    width: 24%;
  }
  
  .input-2-3-width {
    width: 66.6%;
  }
  
  .ml-1 {
    margin-left: 1% !important;
  }
  
  .mr-1 {
    margin-right: 1% !important;
  }
  
  .ml-10 {
    margin-left: 10% !important;
  }

  .mt-1 {
    margin-top: 1% !important;
  }
  
  .mb-1 {
    margin-bottom: 1% !important;
  }
  
  .mb-2 {
    margin-bottom: 2% !important;
  }
  
  .mb-4 {
    margin-bottom: 4% !important;
  }

  .pr-10 {
    padding-right: 10% !important;
  }
  
  .d-none {
    display: none !important;
  }
  
  .primary-blue-btn {
    background-color: #2196F3;
    color: #fff;
  }
  
  .f-w-500{
    font-weight: 500;
  }

  .f-w-0{
    font-weight: 0;
  }

  .active {
    background-color: rgba(255, 255, 255, 0.3);
  }

  // ====================================================== Pagination
.frm_Pagination .temp_Arr {
  display: none;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #e8e8e8;
}

.frm_Pagination {
  padding: 1em 0;
  display: flex;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-wrapper {
  width: 4.5em;
  margin: 0 0.5em;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5em 0;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
  padding-top: 0.5em;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-flex {
  padding: 0 0.5em 0.5em 0.5em;
}

.frm_Pagination .control {
  text-align: right;
  margin-left: auto;
}

::ng-deep .frm_Pagination .control .ngx-pagination a {
  outline: unset;
  background-color: #e8e8e8;
  border-radius: 4px;
  margin: 0 .2em;
}

::ng-deep .frm_Pagination .control .ngx-pagination .current {
  background: #DFEAFF;
  border-radius: 4px;
  margin: 0 .2em;
  color: black !important;
}

::ng-deep .frm_Pagination .control .ngx-pagination .pagination-previous a::before,
::ng-deep .frm_Pagination .control .ngx-pagination .pagination-previous.disabled::before {
  font-family: "Material Icons";
  content: "chevron_left";
  vertical-align: middle;
  transform: scale(1.5);
  margin-right: 0;
  color: #1E2F41;
}

::ng-deep .frm_Pagination .control .ngx-pagination .pagination-next a::after,
::ng-deep .frm_Pagination .control .ngx-pagination .pagination-next.disabled::after {
  font-family: "Material Icons";
  content: "chevron_right";
  vertical-align: middle;
  transform: scale(1.5);
  margin-left: 0;
  color: #1E2F41;
}

.zalo-chat-widget{
  margin-bottom: 35px;
}
.messenger-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.messenger-button img {
  width: 50px;
  height: 50px;
  cursor: pointer;
}
// ====================================================== END Pagination

// Layout DNI====================================================

$layoutDNI: #0084E4;
::ng-deep.layoutDNI .searchBt {
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .formFieldOutline .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .btn_ctrl .applyBtn{
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .colortheme {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI a:hover {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .cfs_searchbar .searchForm .searchBtn{
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .cfs_tbl .mat-button{
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .cfs_tbl .mat-icon{
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .cfs_tbl .mat-header-row {
  border-bottom: 2.5px solid $layoutDNI!important;
}
::ng-deep.layoutDNI .cfs_tbl .mat-column-code span {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .btn-search{
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .listAgency .hl_item .head {
  border-bottom: 3px solid $layoutDNI!important;
}
::ng-deep.layoutDNI .listAgency .hl_item .body .mat-icon {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .layout6 {
  background-color: $layoutDNI!important;
  line-height: 30px;
  margin-top: 0px!important;
}
::ng-deep.layoutDNI .searchForm .rdoAgency .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .account_btn {
  border: 1px solid $layoutDNI!important;
}
::ng-deep.layoutDNI .account_btn:hover {
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .btn_applyOnline {
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .box .procedure-table-data th {
  border-bottom: 1px solid $layoutDNI!important;
}
::ng-deep.layoutDNI .procedure-info-link {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .aoContent .stepper .mat-step-icon-selected {
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .aoContent .stepper .ctrl .btn_prev {
  color: $layoutDNI!important;
  border-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .aoContent .agency {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .psContent .item a .procedureName .procedureCode {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .psContent .item a .mat-icon {
  color: $layoutDNI!important;
}
::ng-deep.layoutDNI .psSearchBar .searchFrm .btn_submit {
  background-color: $layoutDNI!important;
}

::ng-deep .layoutDNI .menuBDG .menu .activeMenu {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .menuBDG .menu .activeMenuReponsive {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI #footer {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color:$layoutDNI!important; /*outer ring color change*/
}
::ng-deep .layoutDNI .mat-radio-button.mat-accent .mat-radio-inner-circle {
  background-color: $layoutDNI!important;   /*inner circle color change*/
}
::ng-deep .layoutDNI #tablecolor1{
  border-bottom: 3px solid $layoutDNI!important;
}
::ng-deep .layoutDNI .psSearchBar .searchFrm button {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .psSearchBar .searchFrm a:nth-child(2) {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .colorTd {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .colorTd3 a:hover{
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .buttonSubmit{
  background-color: $layoutDNI!important;
}
::ng-deep.layoutDNI .redirect .owl-theme .owl-dots .active span {
  background: $layoutDNI!important;
}
::ng-deep.layoutDNI .searchForm .searchBtn {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .frm_Pagination .control .ngx-pagination .current {
  background: $layoutDNI!important;
  color: #fff !important;
}
::ng-deep .layoutDNI .aoContent .ctrl .btn_back {
  color: $layoutDNI!important;
  border-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .detail-procedure .title {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .rsProcedureFee .tblFee .totalCell {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .dialog_title {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .pagination-slice .pagination .control .ngx-pagination .current {
  background: $layoutDNI!important;
}
::ng-deep .layoutDNI .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .loadingBar{
  color: $layoutDNI!important;
}
.loadingBar {
  color: #CE7A58
}
::ng-deep .loadingBar {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .ctrl .btn_next {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .btnPrimary {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .listUploadedFile .wrapList .uploadBtn .mat-button .mat-icon {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .procedureForm .tablecolor1 {
  border-bottom: 3px solid $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .procedureForm .item .head .mat-checkbox-checked .mat-checkbox-background {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .mat-dialog-container .content .highlight {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .mat-step-icon-state-done {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .ctrl .captcha .chkAgree .mat-checkbox-checked .mat-checkbox-background {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .mat-expansion-indicator:after, .mat-expansion-panel-header-description {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .height-expansion a:hover {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .mostsearch .mat-icon {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI a.link :hover {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .box .btn_ctrl{
  color: $layoutDNI!important;
  border-color: $layoutDNI!important; 
}
::ng-deep .layoutDNI .all-content .procedureName {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .success_head .title {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .procedureFee .totalCell {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .applyBtn {
  background-color:  $layoutDNI!important;
}
::ng-deep .layoutDNI .btnLogin {
  background-color:  $layoutDNI!important;
  color: #fff!important;
}
::ng-deep .layoutDNI .btnLogout {
  background-color:  #fff;
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .btnLogout:hover {
  background-color:  #fff!important;
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .activeMenuReponsive {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .listUploadedFile .wrapList .file .icon {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .menuAction .mat-icon {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .tut_header .icon_call {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .tut_suggest .more_item .mat-icon {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .hl_header .icon_call {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .aoContent .stepper .ctrl .billpayment {
  color: $layoutDNI!important;
  border-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .tut_container #main-content .num {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .tut_suggest .more_item:hover {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .st_tabs .mat-tab-label.mat-tab-label-active {
  color: $layoutDNI!important;
}
::ng-deep .layoutDNI .st_tabs.mat-tab-group.mat-primary .mat-ink-bar {
  background-color: $layoutDNI!important;
}
::ng-deep .layoutDNI .listAgency .st_item .head {
  background-color: #E9F6FF!important;
  color: $layoutDNI!important;
}
// ================================================= END Layout DNI