import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { GetFormOriginPopupComponent } from './dialogs/get-form-origin-popup/get-form-origin-popup.component';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { EnvService } from 'src/app/core/service/env.service';
import { Storage468Service } from '../storage468.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { GetFormCitizenStorageOriginPopupComponent } from './dialogs/get-form-citizen-storage-origin-popup/get-form-citizen-storage-origin-popup.component';

@Component({
  selector: 'get-form-origin-item',
  templateUrl: './get-form-origin-item.component.html',
  styleUrls: ['./get-form-origin-item.component.scss']
})
export class GetFormOriginItemComponent implements OnInit {
  @Input() procedureId:string;
  @Input() fullname:string;
  @Input() identityNumber:string;
  @Input() taxcode:string;
  @Input() userId:string;
  @Input() resultCode: string;
  @Output() afterChoose: EventEmitter<object> = new EventEmitter();
  @Output() selectFileEvent = new EventEmitter<{ event: any, detailID: string, typeID: string, scan?: boolean }>(); //IGATESUPP-126962
  config = this.envService.getConfig();
  isPermission:boolean = false;
  selectedLang = localStorage.getItem('language') || 'vi';
  storageLan = false;
  ShowTheNewInterfaceDVCTP = this.deploymentService?.getAppDeployment()?.ShowTheNewInterfaceDVCTP;

  //IGATESUPP-126962
  @Input() ownerFullname: any;
  @Input() elementProcedure: any;
  @Input() formProcedure: any;
  maThanhPhanHS = "";
  tenThanhPhanHS = "";
  formDetail: any;
  formType: any;
  processDefinitionId: "";
  enableNotarizationRepository = this.deploymentService.getAppDeployment().enableNotarizationRepository == 1 ? true : false; //IGATESUPP-126962

  constructor(
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private storage468Service: Storage468Service,
    private deploymentService: DeploymentService,
  ) {
    this.storageLan = this.deploymentService.getAppDeployment()?.storageLan == true ? true : false;
  }
  ngOnInit(): void {

  }
  onClick(){
    if(!!this.taxcode || (!!this.identityNumber && !!this.fullname)){
      const dialogRef = this.dialog.open(GetFormOriginPopupComponent, {
        width: '100%',
        height:'fit-content',
        data: {
          procedureId: this.procedureId,
          fullname:this.fullname,
          identityNumber: this.identityNumber,
          taxcode:this.taxcode,
          userId: this.userId,
          resultCode: this.resultCode
        },
        disableClose: true,
        autoFocus: false,
        panelClass:'is-diglog'
      });

      dialogRef.afterClosed().subscribe(data => {
        if(this.storageLan == true && data){
          if(data.formStorage.isNational == 1){
            this.afterChoose.emit({files: [data], formOrginId: "", formId: data.code, formStorage: data.formStorage});
          } else {
            // const fileIds = element.files.map(i => i.id);
            // this.storage468Service.asyncStorageToFileman(fileIds).subscribe(filemans => {
            //   this.afterChoose.emit({ files: filemans, formOrginId: element?.formOrgin?.id, formId: element?.form?.id, formStorage: element?.form });
            // });
          }
        } else {
          if(!!data && data.length>0){
            data.forEach(element => {
              if(!!element.files){
                // const fileIds = [].concat(data.files).map(i=>i.id);
                const fileIds = element.files.map(i=>i.id);
                this.storage468Service.asyncStorageToFileman(fileIds).subscribe(filemans=>{
                  this.afterChoose.emit({files: filemans, formOrginId: element?.formOrgin?.id, formId: element?.form?.id, formStorage: element?.form});
                });
              }
            });
          }
        }
      })

    }else{
      const msgObj = {
        vi: 'Không tìm thấy thông tin định danh để lấy giấy tờ',
        en: 'Identifier information not found to retrieve the document!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
  }

  //IGATESUPP-126962
  onSelectFiles(event: any, detailID: string, typeID: string, scan?: boolean) {
    this.selectFileEvent.emit({ event, detailID, typeID, scan}); // Gửi dữ liệu về ReceivingComponent
  }

  onClickCitizenStorage() {
    if (this.enableNotarizationRepository) {
      if ((!!this.identityNumber && !!this.fullname)) {
        this.processDefinitionId = this.elementProcedure.map(res => res.case).flat();
        const allForms = this.elementProcedure.map(res => res.form).flat();
        const sortedData = allForms.sort((a, b) => a.id - b.id);
        // Lấy phần tử đầu tiên có status = 1
        const first = sortedData.find(item => item.status === 1);
        if (first) {
          this.maThanhPhanHS = first.code;
          this.tenThanhPhanHS = first.name;
          this.formType = {
            id: first.typeId,
            name: first.typeName
          };
          this.formDetail = this.formProcedure.filter(res => res.form.id === first.id);
        } else {
          console.log("Không có phần tử nào có status = 1");
        }
        const dialogRef = this.dialog.open(GetFormCitizenStorageOriginPopupComponent, {
          width: '100%',
          height: 'fit-content',
          data: {
            processDefinitionId: this.processDefinitionId,
            procedureId: this.procedureId,
            fullname: this.ownerFullname,
            identityNumber: this.identityNumber,
            userId: this.userId,
            maThanhPhanHS: this.maThanhPhanHS,
            tenThanhPhanHS: this.tenThanhPhanHS,
            formType: this.formType,
            formDetail: this.formDetail
          },
          disableClose: true,
          autoFocus: false,
          panelClass: 'is-diglog'
        });

        dialogRef.afterClosed().subscribe((result) => {
          console.log("Kết quả cần", result)
          if (result && result.event) {
            this.onSelectFiles(result.event, result.detailID, result.typeID, true);
          }
        });
      } else {
        const msgObj = {
          vi: 'Không tìm thấy thông tin định danh để lấy giấy tờ',
          en: 'Identifier information not found to retrieve the document!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }
  }
}
