// ::ng-deep {
//     *:focus {
//         outline: none;
//     }

//     .formio-component {
//         margin-top: 1em;

//         .row {
//             display: flex;
//         }

//         .formio-form {
//             margin: 1em 0 2em 0 !important;

//             .fa {
//                 display: none !important;
//             }

//             .col-md-4 {
//                 width: 33%;
//             }

//             label {
//                 color: #1f1f1f !important;
//                 font-weight: 500;
//             }

//             input {
//                 color: #1e2f41;
//                 border: none;
//                 background-color: #8f969c30;
//                 border-radius: 4px;
//                 padding: 0.5em;
//                 font-size: 14px;
//                 min-height: 2em;
//                 // margin: 0.2em 0.5em 1.2em 0;
//                 width: 90%;
//             }

//             input:focus {
//                 outline: #8f969c30;
//             }

//             button {
//                 border: none;
//                 padding: 0 1em;
//                 font-size: 14px;
//                 font-weight: 600;
//                 height: 3.2em;
//                 color: #ce7a58;
//                 background-color: #e8e8e8;
//                 border-radius: 4px;
//                 outline: none;
//                 cursor: pointer;
//             }

//             .formio-choices {
//                 outline: none;
//                 color: #1e2f41;
//                 border: none;
//                 background-color: #8f969c30;
//                 border-radius: 4px;
//                 font-size: 14px;
//                 min-height: 2em;
//                 // margin: 0.2em 0.5em 1.2em 0;
//                 width: 93.5%;

//                 .choices__list {
//                     &.choices__list--single {
//                         padding: 11px;

//                         .choices__button {
//                             display: none;
//                         }
//                     }

//                     &.choices__list--dropdown {
//                         &.is-active {
//                             position: inherit !important;
//                         }
//                     }
//                 }
//             }

//             .alert {
//                 display: none !important;
//             }
//         }

//         .alert {
//             display: none !important;
//         }

//         .formio-errors {
//             font-size: 13px;
//         }

//         &.formio-error-wrapper {
//             border-color: unset !important;
//             padding: 0.5em !important;
//             width: 90%;
//             border-radius: 6px;
//             background-color: #fff0f2;

//             input {
//                 width: 96% !important;
//             }

//             .formio-choices {
//                 width: 100% !important;
//             }
//         }

//         &.formio-warning-wrapper {
//             border-color: unset !important;
//             padding: 0.5em !important;
//             width: 90%;
//             border-radius: 6px;
//             background-color: #fff0f2;

//             input {
//                 width: 96% !important;
//             }

//             .formio-choices {
//                 width: 100% !important;
//             }
//         }
//     }
// }
