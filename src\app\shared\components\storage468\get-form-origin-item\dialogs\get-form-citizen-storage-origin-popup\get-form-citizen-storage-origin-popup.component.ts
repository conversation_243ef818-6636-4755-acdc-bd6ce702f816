import { AfterViewInit, Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DateAdapter} from '@angular/material/core';
import { DetailComponent } from 'src/app/modules/procedure/pages/detail/detail.component';
import { GetFormOriginItemService } from '../../get-form-origin-item.service';
import { MatTableDataSource } from '@angular/material/table';
import { Subject } from 'rxjs';
@Component({
  selector: 'app-get-form-citizen-storage-origin-popup',
  templateUrl: './get-form-citizen-storage-origin-popup.component.html',
  styleUrls: ['./get-form-citizen-storage-origin-popup.component.scss']
})

export class GetFormCitizenStorageOriginPopupComponent implements OnInit, AfterViewInit {
  @ViewChild(DetailComponent) detailComponent!: DetailComponent;
  enableNotarizationRepository = this.deploymentService.getAppDeployment().enableNotarizationRepository ?? false;
  MaTTHC = '';
  dataSource = new MatTableDataSource<any>([]);
  paramsQuery = {
    page: '1',
    size: '10',
  };
  ELEMENTDATA: any = [];
  id: string;
  displayedColumns: string[] = ['stt', 'maHoSo', 'soGiayTo', 'tapTin', 'thuocTinh', 'chon'];
  isSubmitted = false;
  procedureForm = [];
  formChoose: { danhSachTaiLieu: any[] } = { danhSachTaiLieu: [] };
  dataHasValue = 1;
  fileArray = [];

  maThanhPhanHS = "";
  tenThanhPhanHS = "";
  TenChuHoSo = "";
  SoDinhDanhChuHoSo = "";
  fullname = "";
  detailID = "";
  typeID = "";
  identityNumber = "";
  processDefinitionId = "";
  formDetail: any;
  formType: any;

  private _onDestroy = new Subject<void>();
  constructor(
    public dialogRef: MatDialogRef<GetFormCitizenStorageOriginPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: GetFormCitizenStoragePopupDialogModel,
    private dateAdapter: DateAdapter<Date>,
    private deploymentService: DeploymentService,
    private getFormOriginItemService: GetFormOriginItemService,
  ) {
    this.dateAdapter.setLocale('vi');
    this.maThanhPhanHS = data.maThanhPhanHS;
    this.tenThanhPhanHS = data.tenThanhPhanHS;
    this.TenChuHoSo = data.fullname,
    this.SoDinhDanhChuHoSo = data.identityNumber,
    this.processDefinitionId = data.processDefinitionId
    this.formType = data.formType,
    this.formDetail =  data.formDetail
  }

  ngOnInit(): void {
    const requestData = {
      "cccd": this.SoDinhDanhChuHoSo,
      "hoTen": this.TenChuHoSo,
      "ngaySinh": ""
    };
    this.getListFile(requestData);
  }
  
  //Get MaTTHC
  getProcedureDetail(procedureID) {
    this.getFormOriginItemService.getProcedureProcessDefinitionDetail(procedureID).subscribe(async data => {
      this.MaTTHC = data.procedure.code
    }, err => {
      console.log(err);
    });
  }

  getListFile(searchData) {
    this.getFormOriginItemService.downloadFilesFromCitizenStorage(searchData).subscribe(data => {
      if (data && data.danhSachTaiLieu) {
        this.ELEMENTDATA = data.danhSachTaiLieu.map((item, index) => ({
          stt: index + 1,
          maHoSo: this.maThanhPhanHS || 'N/A',
          soGiayTo: this.maThanhPhanHS || 'N/A',
          tapTin: item.tenFile || 'N/A',
          checked: false,
          linkTapTin: item.fileLink,
          fileBase64: item.fileBase64
        }));
        this.dataHasValue = 1;
      } else {
        this.dataHasValue = 0;
        this.ELEMENTDATA = []; // Gán mảng rỗng nếu không có dữ liệu
      }

      // Luôn khởi tạo MatTableDataSource
      this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    }, error => {
      // Trường hợp lỗi API → cũng gán mảng rỗng để hiển thị thông báo
      this.dataHasValue = 0;
      this.ELEMENTDATA = [];
      this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    });
  }
  onSelectFile() {
    const giayToDaChon = this.ELEMENTDATA.filter(item => item.checked);
    if (giayToDaChon.length === 0) {
      alert('Vui lòng chọn ít nhất một giấy tờ.');
      return;
    }
    // Chuyển đổi dữ liệu
    giayToDaChon.forEach(item => {
      this.fileArray.push(this.createFile(item.fileBase64, item.tapTin));
    });
    const event = {
      target: {
        files: this.fileArray
      }
    };
    const detailID = this.formDetail[0];
    const typeID = this.formType;

    this.dialogRef.close({ event, detailID, typeID });
  }
  
  createFile(src: string, fileNameRoot: string) {
    if (!src || typeof src !== 'string') {
      console.error("createFile: Invalid base64 input", src);
      return null;
    }
    let contentType = "";
    let realData = src;

    // Kiểm tra nếu base64 có chứa thông tin MIME type
    if (src.includes(';base64,')) {
      const block = src.split(';base64,');
      if (block.length === 2) {
        contentType = block[0].split(':')[1] || "";
        realData = block[1];
      }
    }
    const filename = `${fileNameRoot}`;
    try {
      const blob = this.b64toBlob(realData, contentType);
      return new File([blob], filename, { type: blob.type });
    } catch (error) {
      console.error("createFile: Error converting base64 to file", error);
      return null;
    }
  }

  b64toBlob(b64Data: string, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
  }

  onDismiss() {
    this.dialogRef.close(null);
  }
  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  ngAfterViewInit() {
    window.addEventListener('message', (event) => {
      const message = event['data'];
      const action = `${message['action']}`;
      const data = message['data'];
      switch (action) {
        case 'get-form-origin-item': this.getFormCitizenStorage(data); break;
      }
    }, false);
  }

  getFormCitizenStorage(data) {
    this.dialogRef.close(data);
  }

}
export interface GetFormCitizenStoragePopupDialogModel {
  processDefinitionId: string,
  procedureId: string,
  fullname: string,
  identityNumber: string,
  userId: string,
  maThanhPhanHS: string,
  tenThanhPhanHS: string,
  formDetail: any,
  formType: any
}