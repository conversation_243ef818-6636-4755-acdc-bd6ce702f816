import {
  AfterContentInit,
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  ViewChild,
  SimpleChanges,
  EventEmitter,
  Renderer2,
  AfterViewInit,

} from '@angular/core';

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import * as BpmnJS from 'bpmn-js/dist/bpmn-navigated-viewer.production.min.js';
import { importDiagram } from 'src/app/data/schema/rx';
import { throwError } from 'rxjs';
import { KeycloakService } from 'keycloak-angular';


@Component({
  selector: 'app-process-diagram',
  templateUrl: './process-diagram.component.html',
  styleUrls: ['./process-diagram.component.scss']
})
export class ProcessDiagramComponent implements AfterContentInit, OnChanges, OnDestroy, AfterViewInit {

  private bpmnJS: BpmnJS;
  private overlays;
  private canvas;
  private currentId: string;
  private elementRegistry: any;

  @ViewChild('ref', { static: true }) private el: ElementRef;
  @Output() private importDone: EventEmitter<any> = new EventEmitter();
  @Output() private elementClick: EventEmitter<any> = new EventEmitter();
  @Output() private taskElements: EventEmitter<any> = new EventEmitter();
  @Input() private url: string;

  @Input() private edit = false;


  constructor(
    private http: HttpClient,
    private keycloak: KeycloakService,
    private renderer: Renderer2
  ) {
    this.bpmnJS = new BpmnJS();
    this.overlays = this.bpmnJS.get('overlays');
    this.bpmnJS.on('import.done', ({ error }) => {
      if (!error) {
        this.bpmnJS.get('canvas').zoom('fit-viewport');
      }
    });
  }

  ngAfterContentInit(): void {
    this.bpmnJS.attachTo(this.el.nativeElement);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.url) {
      this.loadUrl(changes.url.currentValue);
    }
  }

  ngOnDestroy(): void {
    this.bpmnJS.destroy();
  }

  loadUrl(url: string) {
    const token = this.keycloak.getKeycloakInstance().token;
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    return (
      this.http.get(url, { headers, responseType: 'text' }).pipe(catchError(err =>
        throwError(err)), importDiagram(this.bpmnJS)).subscribe((warnings) => {
          this.elementRegistry = this.bpmnJS.get('elementRegistry');
          this.importDone.emit({
            type: 'success',
            warnings
          });
          this.getAlltasks();
        }, (err) => {
          this.importDone.emit({
            type: 'error',
            error: err
          });
        }
        )
    );
  }

  private nodeTypeEditEble = ['bpmn:UserTask', 'bpmn:Process'];


  nodeClick(node) {
    if (!this.edit || !this.nodeTypeEditEble.includes(node.type)) { return; }
    if (this.currentId != null) {
      this.overlays.remove({ element: this.currentId });
    }
    const shape = this.elementRegistry.get(node.id);
    this.currentId = node.id;
    // console.log(this.elementRegistry);
    // console.log(node.id);
    // alert(shape.width + '=' + shape.height)
    this.overlays.add(node.id, {
      position: {
        top: 0,
        left: 0
      },
      html: '<div class="highlight-overlay" style="width:' + shape.width + 'px;height:' + shape.height + 'px">'
    });
    this.elementClick.emit({
      node
    });

  }

  public highlight(value) {
    const shape = this.elementRegistry.get(value);
    this.overlays.add(value, {
      position: {
        bottom: 25,
        left: 0
      },
      html: '<div class="highlight-red" style="width:' + shape.width + 'px;height:' + 25 + 'px">Lỗi!</div>'
    });
  }

  getAlltasks() {
    const listTasks = [];
    this.elementRegistry.forEach(item => {
      if (item.type === 'bpmn:UserTask') {
        listTasks.push(this.makeTaskFromElement(item));
      }
    });
    this.taskElements.emit({
      tasks: listTasks
    });
  }

  makeTaskFromElement(taskEle) {
    const result = {
      activiti: {
        definitionKey: taskEle.id,
        name: taskEle.businessObject.name
      },
      assignee: taskEle.businessObject.$attrs['activiti:assignee'] !== undefined ?
        taskEle.businessObject.$attrs['activiti:assignee'] : null,
      isFirst: false
    };
    return result;
  }

  ngAfterViewInit() {
    this.canvas = this.bpmnJS.get('canvas');
    this.bpmnJS.on('element.click', (event) => this.nodeClick(event.element));
  }

  zoomIn() {
    this.bpmnJS.get('zoomScroll').stepZoom(1);
  }

  zoomOut() {
    this.bpmnJS.get('zoomScroll').stepZoom(-1);
  }

  resetZoom() {
    this.bpmnJS.get('zoomScroll').reset();
  }

  downloadSVG() {
    this.bpmnJS.saveSVG({ format: true }, (error, svg) => {
      if (error) {
        return;
      }

      const svgBlob = new Blob([svg], {
        type: 'image/svg+xml'
      });

      const fileName = Math.random().toString().substring(7) + '.svg';

      const downloadLink = document.createElement('a');
      downloadLink.download = fileName;
      downloadLink.innerHTML = 'Get BPMN SVG';
      downloadLink.href = window.URL.createObjectURL(svgBlob);
      downloadLink.onclick = (event) => {
        document.body.removeChild(event.target as Node);
      };
      downloadLink.style.visibility = 'hidden';
      document.body.appendChild(downloadLink);
      downloadLink.click();
    });
  }
}
