function create_UUID(){
  var sender = localStorage.getItem("sb_sender_name");
  var uuid = '';
  var dt = new Date().getTime();

  if (sender) {
    uuid = sender;
  }else {

    uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      var r = (dt + Math.random()*16)%16 | 0;
      dt = Math.floor(dt/16);
      return (c=='x' ? r :(r&0x3|0x8)).toString(16);
    });
    try {
      localStorage.setItem("sb_sender_name", uuid);
    }
    catch(err) {
      console.log('error', err)
    }
  }
  return uuid;

}

let __protocol = document.location.protocol;
let __baseUrl = __protocol + "//livechat.vnpt.vn";
// let __baseUrl = 'https://livechat.vnpt.vn';

let prefixNameLiveChat = '';
let objPreDefineLiveChat = {
        appCode: 'cb736640-0964-11ec-a1b5-27c0d0df5948',
        themes: '',
        appName: {
          line1: '1022 TPHCM',
          line2: ''
        },
        thumb: '',
        icon_bot:"https://ic-storage.vnpt.vn/smartbot/chatbot_images/972021/99c408b5-ce72-49b2-ad8d-f7eee4478da3.png",
        senderName: create_UUID(),
        styles:{
          head: {
            // fullscreenIcon: 'https://i.ibb.co/r67HvnT/Vector.jpg',
            // closeIcon: 'https://i.ibb.co/GpdytMR/Vector.png',
            bgColor: '#CE7A58',
            text: {
              line: 1,
              line1:{
                color: '#fff',
                fontSize: '16px',
                fontWeight: 700
              },
              line2: {
                color: '#fff',
                fontSize: '16px',
                fontWeight: 700
              }
            }
          },
          border: {
            // bottom: 'solid 1px #CF9355'
          },
          floatButton: {
            bgColor: '#fff'
          },
          chat: {
            reply: {
              bg: '#CE7A58'
            },
            answer: {
              bg: '#CE7A58'
            },
            button: {
              bg: '#CE7A58',
              color: '#fff'
            },
            botIcon:"https://ic-storage.vnpt.vn/smartbot/chatbot_images/972021/99c408b5-ce72-49b2-ad8d-f7eee4478da3.png"
          }
        }
    },
    appCodeHash = window.location.hash.substr(1);
if (appCodeHash.length == 32) {
    objPreDefineLiveChat.appCode = appCodeHash;
}

let vnpt_ai_livechat_script = document.createElement('script');
vnpt_ai_livechat_script.id = 'vnpt_ai_livechat_script';
vnpt_ai_livechat_script.src = __baseUrl + '/vnpt_smartbot.js';
document.body.appendChild(vnpt_ai_livechat_script);

let vnpt_ai_livechat_stylesheet = document.createElement('link');
vnpt_ai_livechat_stylesheet.id = 'vnpt_ai_livechat_script';
vnpt_ai_livechat_stylesheet.rel = 'stylesheet';
vnpt_ai_livechat_stylesheet.href = __baseUrl + '/vnpt_smartbot.css';
document.body.appendChild(vnpt_ai_livechat_stylesheet);

vnpt_ai_livechat_script.onload = function () {
    vnpt_ai_render_chatbox(objPreDefineLiveChat, __baseUrl, 'livechat.vnpt.ai:443')
}
