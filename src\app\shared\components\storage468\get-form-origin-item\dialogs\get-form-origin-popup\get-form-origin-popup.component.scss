.all-content{
    width: 100%;
    overflow-x:'hidden';
    .close-button{
        float: right;
        top: -15px;
        right: -15px;
    }
    iframe{
        height: 85vh;
        border: none;
    }
    .procedureName{
        font-weight: 500;
        font-size: 17px;
        line-height: 27px;
        color: #ce7a58;
        padding-left: 1%;
    }
}

.tabs {
    background-color: #CCCCCC;
    margin-left: 20px;
    margin-top: 10px;
    margin-right: 19px;
}

.label {
    color: gray;
    font-size: 15px;
}
.value {
    color: black;
    font-weight: 500;
}
.card-body {
    .no-data {
        text-align: center;
        padding: 10px;

        i {
            color: red;
        }
    }
}
.responsive-table {
    width: 100%;
    table-layout: fixed;
}
.titleTable{
    padding: 5px 0;
    background-color: #cbc9c9;
    margin-top: 10px;
}
.custom-color{
    color:#007aff !important;
}