<div style="text-align: center; margin-bottom: 1rem;">
    <span class="title"><PERSON>ọn mẫu chữ ký</span>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <mat-form-field appearance="fill" fxFlex="100">
        <mat-label>Mẫu chữ ký</mat-label>
        <mat-select class="selectType" [(value)]="currentIndex">
          <mat-option *ngFor="let item of signatures; let i = index" [value]="i">{{item.name}}</mat-option>
        </mat-select>
      </mat-form-field>
</div>

<div fxLayout="row" fxLayoutAlign="center" fxLayoutGap="1rem">
    <button mat-raised-button class="btn" color="primary" (click)="onConfirm()">
        <span>Xác nhận</span>
    </button>
    <button mat-raised-button class="btn" (click)="onCancel()">
        <span>Bỏ qua</span>
    </button>
</div>