import {AfterViewInit, Component, Inject, OnInit} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { GetFormOriginItemService } from '../../get-form-origin-item.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DomSanitizer } from '@angular/platform-browser';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { LanService } from 'src/app/data/service/lan/lan.service';
import { EnvService } from 'src/app/core/service/env.service';
import { FilemanService } from 'src/app/data/service/svc-fileman/fileman.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';

@Component({
  selector: 'app-get-form-origin-popup',
  templateUrl: './get-form-origin-popup.component.html',
  styleUrls: ['./get-form-origin-popup.component.scss']
})
export class GetFormOriginPopupComponent implements OnInit, AfterViewInit {
  Element;
  procedureId='';
  identityNumber='';
  taxcode='';
  fullname='';
  storage468 = this.deploymentService.getAppDeployment()?.storage468;
  resultCode = [];
  domainKhoTTHC: any=this.storage468?.webDocAdmURL ? this.storage468?.webDocAdmURL : 'https://quantrikhotest.vnptigate.vn'

  storageLan = false;
  dataFromProvince = [];
  dataFromNation = [];
  config;
  userId: any;

  ShowTheNewInterfaceDVCTP = this.deploymentService?.getAppDeployment()?.ShowTheNewInterfaceDVCTP;

  constructor(
    public dialogRef: MatDialogRef<GetFormOriginPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: GetFormOriginPopupDialogModel,
    private getFormOriginItemService:GetFormOriginItemService,
    private sanitizer: DomSanitizer,
    private deploymentService: DeploymentService,
    private lanService: LanService,
    private envService: EnvService,
    private filemanService: FilemanService,
    private snackbar: SnackbarService
  ) { 
    this.procedureId = data.procedureId;
    this.fullname = data.fullname
    this.identityNumber = data.identityNumber;
    this.taxcode = data.taxcode;
    this.userId = data.userId
    this.resultCode = data.resultCode;

    this.storageLan = this.deploymentService.getAppDeployment()?.storageLan == true ? true : false;
    this.config = this.envService.getConfig();
  }

  ngOnInit(): void {
    if(this.storageLan == true){
      this.getFormOriginItemService.getComponentsProcedure(this.procedureId).subscribe(data => {
        this.Element = data;
        this.getFormStorageProvince();
      });
    } else {
      this.changeURL();
      this.getFormOriginItemService.getComponentsProcedure(this.procedureId).subscribe(data => {
        this.Element=data;
      });
    }
  }

  ngAfterViewInit(){
    if(this.storageLan == false){
      window.addEventListener('message', (event) => {
        const message = event['data'];
        const action = `${message['action']}`;
        const data = message['data'];
        switch (action){
          case 'get-form-origin': this.getFormOrigin(data); break;
        }
      },false);
    }
  }

  getFormOrigin(data){
    console.log(data);
    this.dialogRef.close(data);
  }

  onDismiss() {
    this.dialogRef.close(null);
  }
  changeURL(){
    this.domainKhoTTHC +='/vi/helper/get-form-origin?procedureId='+this.procedureId+'&system=igate2'
    if(!!this.fullname){
      this.domainKhoTTHC += '&fullname='+this.fullname;
    }
    if(!!this.identityNumber){
      this.domainKhoTTHC += '&identityNumber='+this.identityNumber;
    }
    if(!!this.taxcode){
      this.domainKhoTTHC += '&taxcode='+this.taxcode;
    }
    this.domainKhoTTHC = this.sanitizer.bypassSecurityTrustResourceUrl(this.domainKhoTTHC);
  }

  tabIndex = 0;
  tabSelected = 0;
  onTabChange(event: MatTabChangeEvent) {
    this.tabSelected = event.index;
    if(this.tabSelected == 0){
      this.getFormStorageProvince();
    } else {
      this.getFormStorageNation();
    }
  }

  hidenDetailsWhileGetForms = true;
  onChangeViewDetail(){
    this.hidenDetailsWhileGetForms = !this.hidenDetailsWhileGetForms;
  }
  onChangeTab($event){
    // this.provider = $event==0?'local':'national';
    // if(this.provider=='national' && this.nationalElements==undefined){
    //   this.ngxService.start();
    //   this.getLocalOrNationalForms();
    // }
  }
  getDisplayedColumns(com, provider):string[]{
    let displayedColumns:string[] = ['index', 'code', 'filename', 'action'];
    if(provider=='local'){

    }
    return displayedColumns;
  }
  getFormStorageNation(){
    let listFormOriginCode = [];
    for (let index = 0; index < this.Element.components.length; index++) {
      listFormOriginCode.push({ code: this.Element.components[index].formOriginNationalCode, number: "" });
    }
    let boby = {
      procedureCode: this.Element.nationalProcedureCode,
      formOriginCode: listFormOriginCode,
      identityNumber: this.identityNumber,
    }

    this.lanService.getFormFromNation(boby).subscribe((dataResponse) => {
      this.dataFromNation = dataResponse.data;
    });
  }
  getFormStorageProvince(){
    let listFormOriginCode = [];
    for (let index = 0; index < this.resultCode.length; index++) {
      listFormOriginCode.push({ code: this.resultCode[index] });
    }
    let boby = {
      identityNumber: this.identityNumber,
      procedureCode: this.Element.nationalProcedureCode,
      formOriginCode: listFormOriginCode
    }
    this.lanService.getFormFromProvince(boby).subscribe((dataResponse) => {
      this.dataFromProvince = dataResponse.data;
    });
  }
  getFileStorage(hashId, event){
    event.preventDefault();
    this.lanService.getFileFromStorage(hashId).subscribe({
      next: (response: any) => {
        const blob = response.body;
        const contentDisposition = response.headers.get('Content-Disposition');
        const filename = contentDisposition?.split('filename=')[1] || 'file-download';

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename.replaceAll("\"", '');
        a.click();
        window.URL.revokeObjectURL(url);
      },
      error: (err) => {
        console.error('Error during file download:', err);
      },
    });
  }
  getFileNation(url, filename, event){
    event.preventDefault();
    this.lanService.getFileFromNation(url, filename).subscribe({
      next: (response: any) => {
        const blob = response.body;
        const contentDisposition = response.headers.get('Content-Disposition');
        const filename = contentDisposition?.split('filename=')[1] || 'file-download';

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename.replaceAll("\"", '');
        a.click();
        window.URL.revokeObjectURL(url);
      },
      error: (err) => {
        console.error('Error during file download:', err);
      },
    });
  }
  useFileStorage(hashId, code){
    let listAcceptExt = this.config.procedureFormAcceptFileExtension;
    let listAcceptFileType = this.config.procedureFormAcceptFileType;
    this.lanService.getFileFromStorage(hashId).subscribe({
      next: (response: any) => {
        const blob = response.body;
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = contentDisposition?.split('filename=')[1] || 'file-download';
        filename = filename.replaceAll("\"", '');
        let getExtension = filename.split('.').pop();
        const index = listAcceptExt.indexOf("." + getExtension.toUpperCase());
        let extention = listAcceptFileType[index];
        let file = new File([blob], filename, {
          type: extention,
          lastModified: Date.now()
        });
        this.filemanService.uploadMultiFile([file], this.userId, false).subscribe(res => {
          let file = res[0];
          file.code = code;
          file.formStorage = {
            isNational: 1,
            repCode: code,
            reused: 1,
          }
          this.getFormOrigin(file);
        });
      },
      error: (err) => {
        console.error('Error during file download:', err);
      },
    });
  }
  useFileStorageNation(urlFile, filename, code){
    let listAcceptExt = this.config.procedureFormAcceptFileExtension;
    let listAcceptFileType = this.config.procedureFormAcceptFileType;
    this.lanService.getFileFromNation(urlFile, filename).subscribe({
      next: (response: any) => {
        const blob = response.body;
        const contentDisposition = response.headers.get('Content-Disposition');
        let filenameRes = contentDisposition?.split('filename=')[1] || 'file-download';
        filename = filename.replaceAll("\"", '');
        let getExtension = filename.split('.').pop();
        const index = listAcceptExt.indexOf("." + getExtension.toUpperCase());
        let extention = listAcceptFileType[index];
        let file = new File([blob], filenameRes || filename, {
          type: extention,
          lastModified: Date.now()
        });
        this.filemanService.uploadMultiFile([file], this.userId, false).subscribe(res => {
          let file = res[0];
          file.code = code;
          file.formStorage = {
            isNational: 2,
            repCode: code,
            reused: 1,
          }
          this.getFormOrigin(file);
        });
      },
      error: (err) => {
        console.error('Error during file download:', err);
      },
    });
  }
  async getLocalForm(com, element) {
    let selectedLang = localStorage.getItem('language');
    let files = element?.files;
    if (!files) {
      if(!element?.identityDoc){
        const msg = {
          vi: 'Không tìm thấy mã hồ sơ',
          en: 'Dossier code not found'
        };
        this.snackbar.openSnackBar(0, '', msg[selectedLang], 'error_notification', 3000);
        return;
      }
      let body = {
        links: element?.links,
        dossierCode: element?.identityDoc,
        formOriginCode: element?.formOrgin?.id,
        system: "igate2"
      };
      //files = await this.getFormOriginService.getNationalFile(body);
    }
    const data = {
      form: {
        id: com?.id,
        code: element?.formOrgin?.id,
        repCode: element?.repCode,
        isNational: 2,
        reused: com?.reusedType
      },
      files: files,
      ownerIdentity: element?.ownerIdentity
    };
    
  }
  viewFile(file, provider){
    // if(!this.canReadFile){
    //   return;
    // }
    // this.fileObject=[];
    // if(provider=='local'){
    //   return new Promise<void>(resolve => {
    //     this.directoryService.downloadFile(file?.id).subscribe(response => {
    //       this.fileObject.push(response);
    //       resolve();
    //       this.providerLocal=true;
    //       setTimeout(() => {
    //         this.scrollDiv.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    //       }, 50);
    //     }, err => {
    //       resolve();
    //     });
    //   })
    // }else{
    //   this.fileObject.push(this.sanitizer.bypassSecurityTrustResourceUrl(file?.url));
    //   this.providerLocal=false;
    //   setTimeout(() => {
    //     this.scrollDiv.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    //   }, 50);
    // }
  }
}
export interface GetFormOriginPopupDialogModel {
  procedureId: string;
  fullname: string;
  identityNumber: string;
  taxcode: string;
  userId: string;
  resultCode?: string[];
}
