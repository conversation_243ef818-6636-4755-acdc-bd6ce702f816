import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ProcedureFrequentComponent } from './procedure-frequent.component';

describe('ProcedureFrequentComponent', () => {
  let component: ProcedureFrequentComponent;
  let fixture: ComponentFixture<ProcedureFrequentComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ProcedureFrequentComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcedureFrequentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
