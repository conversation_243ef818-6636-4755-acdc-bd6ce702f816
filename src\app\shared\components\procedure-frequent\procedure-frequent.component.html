<div class="mostsearch">
    <div class="head" [ngStyle]="useCustomStyleHPG ? {
        'background-color': '#ce7a58',
        'background-image': 'url(https://staticv2.vnptigate.vn/logo/trong-dong.png)',
        'background-position': '50% 70%',
        'background-repeat': 'no-repeat',
        'background-size': '100%',
        'color': 'white'
      } : {}">
        <span i18n>Thủ tục thường gặp</span>
    </div>
    <div *ngIf="ShowTheNewInterfaceDVCTP == 1 ; else hidenTheNewInterfaceDVCTP">
        <div class="list">
            <div fxLayout="row" fxLayoutAlign="center" *ngFor="let proc of listProcedureFrequent">
                <div fxFlex="90" class="margin-procedure">
                    <div fxFlex="10">
                        <mat-icon style="color:#007aff; font-size: 23px;">keyboard_arrow_right</mat-icon>
                    </div>
                    <div fxFlex="86">
                        <a class="linkNew" routerLink="/procedure/detail/{{proc.id}}">
                            <span>{{proc.name}}</span>
                            <br>
                            <span style="color: rgb(187, 185, 185);">{{proc.agencyName}}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ng-template #hidenTheNewInterfaceDVCTP>
        <div class="list">
            <div fxLayout="row" fxLayoutAlign="center" *ngFor="let proc of listProcedureFrequent">
                <div fxFlex="90" class="margin-procedure">
                    <div fxFlex="10">
                        <mat-icon style="color:brown; font-size: 23px;">keyboard_arrow_right</mat-icon>
                    </div>
                    <div fxFlex="86">
                        <a class="link" routerLink="/procedure/detail/{{proc.id}}">
                            <span>{{proc.name}}</span>
                            <br>
                            <span style="color: rgb(187, 185, 185);">{{proc.agencyName}}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
          </ng-template>
</div>
