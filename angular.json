{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"padsvc": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"showCircularDependencies": false, "outputPath": "dist/web", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "i18nFile": "src/locale/messages.vi.xlf", "i18nFormat": "xlf", "i18nLocale": "vi", "i18nMissingTranslation": "error", "assets": ["src/assets/img/quoc-huy.png", "src/assets", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "./node_modules/angular-formio.css/angular-formio.css", "src/styles.scss", "./node_modules/font-awesome/css/font-awesome.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.carousel.min.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.theme.default.min.css", "node_modules/slick-carousel/slick/slick.css", "node_modules/slick-carousel/slick/slick-theme.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/slick-carousel/slick/slick.min.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "100kb"}]}, "production-vi": {"outputPath": "dist/web/vi", "baseHref": "/vi/", "i18nFile": "src/locale/messages.vi.xlf", "i18nFormat": "xlf", "i18nLocale": "vi", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "100kb"}]}, "production-en": {"outputPath": "dist/web/en", "baseHref": "/en/", "i18nFile": "src/locale/messages.en.xlf", "i18nFormat": "xlf", "i18nLocale": "en", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "100kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "padsvc:build"}, "configurations": {"production": {"browserTarget": "padsvc:build:production"}, "production-vi": {"browserTarget": "padsvc:build:production-vi"}, "production-en": {"browserTarget": "padsvc:build:production-en"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "padsvc:build", "outputPath": "src/locale"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/assets/img/quoc-huy.png", "src/assets", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "./node_modules/angular-formio.css/angular-formio.css", "src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "padsvc:serve"}, "configurations": {"production": {"devServerTarget": "padsvc:serve:production"}}}}}}, "defaultProject": "padsvc", "cli": {"analytics": "19e983ca-63ab-4b30-a33e-129cbef7dfcd"}}