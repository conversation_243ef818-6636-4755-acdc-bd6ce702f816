import {Component, Input, OnInit} from '@angular/core';
import {GetFormOrginComponent} from "shared/components/storage468/get-form-storage468/get-form-orgin/get-form-orgin.component";
import {SnackbarService} from "data/service/snackbar/snackbar.service";
import {EnvService} from "core/service/env.service";
import {IFormOrginInFoInput, IFileInfo} from "shared/components/storage468/storage468.schema";
import {Storage468Service} from "shared/components/storage468/storage468.service";
import {MatDialog} from "@angular/material/dialog";
import {SaveFormOrginComponent} from "shared/components/storage468/save-into-storage468/save-form-orgin/save-form-orgin.component";
import { FormOrginService } from 'data/service/form-orgin/form-orgin.service';

@Component({
  selector: 'btn-save-form-orgin',
  templateUrl: './btn-save-form-orgin.component.html',
  styleUrls: ['./btn-save-form-orgin.component.scss']
})
export class BtnSaveFormOrginComponent{
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  @Input() getFormOrgin:GetFormOrginComponent;
  @Input() btnClass:string;
  @Input() form:any;
  @Input() fileId:string;
  @Input() files:any;
  @Input() procedureId:string;
  @Input() filenameKey:string;
  @Input() fileIdKey:string;
  @Input() checkOverride:string='false';
  oldIdentityDoc = undefined;
  title:string;
  formId;

  constructor(private snackbarService: SnackbarService,
              private envService: EnvService,
              private dialog: MatDialog,
              private formOrginService: FormOrginService,
              private storage468Service: Storage468Service) {
  }

  getDDMMYYYY(isoDate){
    if(isoDate == '' || isoDate == null) return '';
    let date = new Date(isoDate);
    let year = date.getFullYear();
    let month = date.getMonth()+1;
    let day = date.getDate();
    let mm = `${month}`;
    let dd = `${day}`

    if (day < 10) {
      dd = `0${day}`
    }
    if (month < 10) {
      mm = `0${month}`;
    }
    return `${dd}/${mm}/${year}`;
  }

  getErrorMessage(){
    let errMsg = '';
    if(!this.getFormOrgin?.fullname)
      errMsg += `, Họ và tên`;
    if(!this.getFormOrgin?.identityNumber)
      errMsg += `, CMND/CCCD`;
    if(!this.getFormOrgin.birthday)
      errMsg += `, Ngày sinh`;
    if(errMsg){
      errMsg = errMsg.substring(1);
    }
    return errMsg;
  }

  openSaveOutputDiglog(getFormOrgin:GetFormOrginComponent,
                       procedureId:string,
                       files:any,
                       fileId:string,
                       fileIdKey:string,
                       filenameKey:string){
    this.getFormOrgin = getFormOrgin;
    this.procedureId = procedureId;
    this.files = files;
    this.fileId = fileId;
    this.filenameKey = filenameKey;
    this.fileIdKey = fileIdKey;
    this.openSaveDiglog();
  }

  async openSaveDiglog() {
    const err = this.getErrorMessage();
    if (err) {
      const msgObj = {
        vi: `Vui lòng nhập: ${err}`,
        en: `Vui lòng nhập: ${err}`
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
    }
    if (this.procedureId) {
      const formOutput = await this.storage468Service.getFormOutput(this.procedureId).toPromise().catch(error => {
        return null;
      });
      if (!formOutput) {
        const msgObj = {
          vi: 'Thủ thục này chưa cấu hình giấy tờ đầu ra',
          en: 'Thủ thục này chưa cấu hình giấy tờ đầu ra'
        };
        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
      }
      this.title = `Lưu ${formOutput.name[0].name.toLowerCase()} vào kho`;
      this.formId = formOutput.id;
    } else {
      this.title = `Lưu ${this.form?.name?.toLowerCase()} vào kho`;
      this.formId = this.form.id;
    }

    let files:IFileInfo[] = [];
    if(this.filenameKey || this.fileIdKey){
      [].concat(this.files).forEach(item=>{
        let fileItem:IFileInfo = {};
        if(this.fileIdKey)
          fileItem.id = item[this.fileIdKey];
        else
          fileItem.id = item['id'];
        if(this.filenameKey)
          fileItem.filename = item[this.filenameKey];
        else
          fileItem.filename = item['filename'];
        files.push(fileItem);
      });
    }else {
      files = this.files;
    }
    let _birthday = this.getDDMMYYYY(this.getFormOrgin.birthday);
    let formInfo = await this.formOrginService.getFormInfoPromise(this.formId).then(rs=>rs);
    let formOrginInfo = await this.formOrginService.getFormOrginById(formInfo.formOrgin.id).then(rs=>rs);
    let docIdentityName = formOrginInfo.docIdentityName;
    if(this.checkOverride == 'true' && formOrginInfo.multiple == false){
      let directoryInfo = await this.storage468Service.getDirectoryInfo(formInfo.formOrgin.id,this.getFormOrgin.identityNumber,this.getFormOrgin.fullname,_birthday).toPromise().then(rs=>rs);
      let arr = [].concat(directoryInfo);
      if(arr.length == 1){
          this.oldIdentityDoc = arr[0]?.identityDoc;
      }
    }
    let formOrginInFoInput: IFormOrginInFoInput = {
      formOrginId: formOrginInfo.id,
      identityNumber: this.getFormOrgin.identityNumber,
      fullname: this.getFormOrgin.fullname,
      birthday: _birthday,
      isIdentity: formOrginInfo.isIdentity,
      fileId: this.fileId,
      files: files,
      expire: formOrginInfo.expire,
      oldIdentityDoc: this.oldIdentityDoc
    }

    this.dialog.open(SaveFormOrginComponent, {
      width: '800px',
      data: {
        formOrginInfo: formOrginInFoInput,
        title: this.title,
        docIdentityName: docIdentityName
      },
      disableClose: true,
      autoFocus: false
    });
  }
}
