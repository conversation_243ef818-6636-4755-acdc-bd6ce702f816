.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #CE7A58;
    overflow-wrap: anywhere;
}

::ng-deep .applyBtn {
    margin: 1em .5em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
    min-width: 20%;

    .mat-icon {
        vertical-align: middle;
        margin-right: .2em;
    }
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #F5F5F5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar {
    width: 5px;
    background-color: #F5F5F5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
    background-color: #44444450;
}

.dialog_content {
    font-size: 15px;
    height: 60vh;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.12);
}

.diagram-container {
    height: 100%;
    width: 100%;
  }