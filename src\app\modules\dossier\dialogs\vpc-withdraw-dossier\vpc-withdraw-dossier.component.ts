import { Component, OnInit, Inject } from '@angular/core';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { AdapterService } from 'src/app/data/service/svc-adapter/adapter.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { EnvService } from 'src/app/core/service/env.service';
import { ALLOW_WITHDRAW_STATUS } from 'src/app/data/service/shared/config.service';
import { DatePipe } from '@angular/common';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { UserService } from 'src/app/data/service/user/user.service';
import { SocialProtectionKTMService } from 'src/app/data/service/ktm-social-protection/social-protection-ktm.service';
import { SocialProtectionService } from 'src/app/data/service/social-protection/social-protection.service';
import {LogmanService} from 'data/service/logman/logman.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { KeycloakService } from 'keycloak-angular';
import * as tUtils from "data/service/thoai.service";
import {TrinamService} from "modules/hbh/trinam.service";
import { VpcdossierService } from 'src/app/data/service/dossier/vpcdossier.service';

@Component({
  selector: 'app-vpc-withdraw-dossier',
  templateUrl: './vpc-withdraw-dossier.component.html',
  styleUrls: ['./vpc-withdraw-dossier.component.scss']
})
export class VpcWithdrawDossierComponent implements OnInit {

  public Editor = ClassicEditor;
  isCKMaxLength = false;
  ckeditorMaxLengthNotification = "";
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'EasyImage'],
    toolbarLocation: 'bottom',
    toolbar: [ 'bold', 'italic', '|', 'undo', 'redo']
  };

  env: any = this.deploymentService.getAppDeployment()?.env;
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') ? localStorage.getItem('language') : 'vi';
  dossierId: string;
  dossierCode:string;
  vpcTypeWithDraw: string;
  commentContent = '';
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  showWithdrawCommentTextArea = this.deploymentService.env.OS_HCM?.showWithdrawCommentTextArea ? this.deploymentService.env.OS_HCM.showWithdrawCommentTextArea : false;
  isCheckReceivedDossier = this.deploymentService.env.OS_HCM.isCheckReceivedDossier;
  allowWithdrawStatus = ALLOW_WITHDRAW_STATUS;
  vpcwithdrawprocess = this.deploymentService.getAppDeployment()?.vpcWithdrawRequest ? this.deploymentService.getAppDeployment()?.vpcWithdrawRequest : 0;
  ktmDossierToSyncStatusBLDTBXH = null;
  ktmSyncToBLDTBXHConfigId = this.deploymentService.env.OS_KTM.syncToBLDTBXHConfigId;
  ktmEnableSyncToBLDTBXH = this.deploymentService.env.OS_KTM.enableSyncToBLDTBXH;
  dossierToSyncStatusBLDTBXH = null;
  syncToBLDTBXHConfigId = this.deploymentService.env.OS_QNM.syncToBLDTBXHConfigId;
  enableSyncToBLDTBXH = this.deploymentService.env.OS_QNM.enableSyncToBLDTBXH;
  isSyncConstructKTM=this.deploymentService.env?.OS_KTM.isSyncConstructKTM;
  constructConfigId=this.deploymentService.env?.OS_KTM.constructConfigId;
  procedureSyncBXD=this.deploymentService.env?.OS_KTM.procedureSyncBXD;
  sendLogCitizenHPG =  this.deploymentService.getAppDeployment()?.logCitizenDrawDossier?this.deploymentService.getAppDeployment()?.logCitizenDrawDossier:false;
  codeMap=""

  // file upload
  acceptFileExtension = this.config.procedureFormAcceptFileExtension;
  acceptFileType = this.config.procedureFormAcceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  blankVal: any;
  uploadedImage = [];
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  uploaded: boolean;

  // user info
  fullname: string;
  userName: string;
  userId: string;
  accountId: string;

  allowOfficerToApproveWithdrawRequest = this.deploymentService.env.OS_HCM.allowOfficerToApproveWithdrawRequest.enable;
  staticUrl = this.deploymentService.getAppDeployment()?.env?.staticUrl ? this.deploymentService.getAppDeployment()?.env?.staticUrl : 'https://staticv2.vnptigate.vn';
  requireAttachment = false;

  constructor(
    private dossierService: DossierService,
    private vpcdossierService: VpcdossierService,
    private deploymentService: DeploymentService,
    private padmanService: PadmanService,
    private adapterService: AdapterService,
    public dialogRef: MatDialogRef<VpcWithdrawDossierComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmWithdrawDialogModel,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private humanService: UserService,
    private socialProtectionKTMService: SocialProtectionKTMService,
    private socialProtectionService: SocialProtectionService,
    private datePipe: DatePipe,
    private procedureService: ProcedureService,
    private logmanService: LogmanService,
    private notiService: NotificationService,
    private keycloakService: KeycloakService,
    private trinamService: TrinamService,
  ) {
    this.env = this.deploymentService.getAppDeployment()?.env;
    this.dossierId = data.id;
    this.dossierCode = data.dossierCode;
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.checkRequireAttachment();
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
  }

  onConfirm() {
    this.padmanService.getDossierDetail(this.dossierId).subscribe(async data => {
      if ((this.isCheckReceivedDossier != 1 && this.allowWithdrawStatus.includes(data.dossierStatus?.id)) || (this.isCheckReceivedDossier == 1 && data.dossierStatus?.id == 0)
          || (this.checkDossierAllowToWithdraw(data.agency) && this.allowWithdrawStatus.includes(data.dossierStatus?.id)))
      {
        if (this.showWithdrawCommentTextArea || this.requireAttachment || this.vpcwithdrawprocess == 1) {
          if (this.commentContent.trim() === ''){
            const msgObj = {
              vi: 'Vui lòng nhập lý do rút hồ sơ!',
              en: 'Please enter a reason for withdrawal!'
            };
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          }

          if (this.isCKMaxLength){
            let msgObj;
            if (this.ckeditorMaxLength > 0) {
              msgObj = {
                vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
                en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
              };
            } else {
              msgObj = {
                vi: 'Nội dung không quá 500 ký tự!',
                en: 'Content must not exceed 500 characters!'
              };
            }
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          }

          if (this.requireAttachment) {
            if (this.files.length === 0) {
              this.snackbarService.openSnackBar(0, '', 'Vui lòng đính kèm văn bản đề nghị rút hồ sơ!', 'error_notification', this.config.expiredTime);
            }
          }
          if (this.vpcwithdrawprocess == 1) {
            if (!this.vpcTypeWithDraw) {
              return this.snackbarService.openSnackBar(0, '', 'Vui lòng chọn hình thức rút hồ sơ!', 'error_notification', this.config.expiredTime);
            }
          }

          if (!this.isCKMaxLength && this.commentContent.trim() !== '' &&
          (
            !this.requireAttachment ||
            (this.requireAttachment && this.files.length !== 0) ||
            (this.vpcwithdrawprocess == 1 && this.vpcTypeWithDraw)
          )) {
            let requestBody = {
              citizenWithdrawComment: this.commentContent.trim()
            }
            if(this.vpcwithdrawprocess == 1){
              if (this.files.length > 0) {
                  this.vpcUploadMultiFile(this.files, this.accountId);
              } else {
                this.putDossierWithdrawWithComment();
              }
            }
            else{
              this.dossierService.withdrawDossierWithComment(this.dossierId, requestBody).subscribe(res => {
                // IGATESUPP-19677 cancel vnpost dossier
                if (this.env?.OS_BDG?.isEnableUseVNPost && this.env?.OS_BDG?.isEnableUseVNPost === true) {
                  this.padmanService.getDossierDetail(this.dossierId).subscribe(data1 => {
                    if (data1?.vnpostStatus !== undefined) {
                      const search = '?config-id=' + this.env?.OS_BDG?.configLGSPMinhTueId + '&CustomerCode=' + data1.vnpostStatus.customerCode + '&OrderNumber=' + data1.id;
                      this.adapterService.getCancelVnpostDossier(search).subscribe(datav => {
                        // console.log(datav);
                      });
                    }
                  }, err => {
                    console.log(err);
                  });
                }

                if (res.affectedRows === 1) {

                  if(this.ktmEnableSyncToBLDTBXH){
                    this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
                      const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
                      if(this.socialProtectionKTMService.checkIfDossierNeedSyncBTXH(procedureData)){
                        this.syncDossierStatusKTMToBLDTBXH(dossierData);
                      }
                    });
                  }
                  // Lay thong tin ho so de dong bo len Bo LDTBXH
                  this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
                    const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
                    if(this.enableSyncToBLDTBXH && this.socialProtectionService.checkIfDossierNeedSyncBTXH(procedureData)){
                      this.syncDossierStatusToBLDTBXH(dossierData);
                    }
                    if (this.deploymentService?.env?.OS_HBH?.enableLDXHTriNam && !!procedureData?.btxhcode)  {
                        Object.assign(dossierData, {contentTask: 'Công dân rút hồ sơ'})
                        this.trinamService.syncTaskLDXH(dossierData);
                    }
                  });

                  if(this.isSyncConstructKTM){
                    this.dossierService.getDossierDetail(this.dossierId).subscribe(dataDossier => {
                      let procedureList = this.procedureSyncBXD;
                      for (let procedureElement of procedureList) {
                        if (dataDossier.procedure.code === procedureElement) {
                          this.syncDossierKTMToBXD();
                        }
                      }
                    })
                  }

                  // Gửi tin nhắn thông báo cho cán bộ duyệt yêu cầu rút hồ sơ
                  if (this.allowOfficerToApproveWithdrawRequest && data.dossierStatus?.id != 0) {
                    this.postCommentAndSendSms(data, this.commentContent.trim());
                  }

                  this.dialogRef.close(true);
                  const dataLog = {
                    dossierId: this.dossierId,
                    code : this.dossierCode,
                    body: requestBody
                  };
                  this.logmanService.postUserEventsLog('withdrawDossier', dataLog).subscribe();
                }
                else {
                  this.dialogRef.close(false);
                }
              }, err => {
                this.dialogRef.close(false);
              });
            }


            // Test lay thong tin ho so de dong bo len Bo LDTBXH
            // if(this.ktmEnableSyncToBLDTBXH){
            //   this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
            //     const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
            //     if(this.socialProtectionKTMService.checkIfDossierNeedSyncBTXH(procedureData)){
            //       this.syncDossierStatusKTMToBLDTBXH(dossierData);
            //     }
            //   });
            // }
            // Test lay thong tin ho so de dong bo len Bo LDTBXH
            // this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
            //   const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
            //   if(this.enableSyncToBLDTBXH && this.socialProtectionService.checkIfDossierNeedSyncBTXH(procedureData)){
            //     this.syncDossierStatusToBLDTBXH(dossierData);
            //   }
            // });
          }
        }
        else {
          this.dossierService.withdrawDossier(this.dossierId).subscribe(res => {
            // IGATESUPP-19677 cancel vnpost dossier
            if (this.env?.OS_BDG?.isEnableUseVNPost && this.env?.OS_BDG?.isEnableUseVNPost === true) {
              this.padmanService.getDossierDetail(this.dossierId).subscribe(data2 => {
                if (data2?.vnpostStatus !== undefined) {
                  const search = '?config-id=' + this.env?.OS_BDG?.configLGSPMinhTueId + '&CustomerCode=' + data2.vnpostStatus.customerCode + '&OrderNumber=' + data2.id;
                  this.adapterService.getCancelVnpostDossier(search).subscribe(datav => {
                    // console.log(data);
                  });
                }
              }, err => {
                console.log(err);
              });
            }
            if (res.affectedRows === 1) {
              this.dialogRef.close(true);
              const dataLog = {
                dossierId: this.dossierId,
                code : this.dossierCode
              };
              this.logmanService.postUserEventsLog('withdrawDossier', dataLog).subscribe();
              // IGATESUPP-92502 Thêm log khi công dân tút hồ sơ thành công
              if(this.sendLogCitizenHPG){
                const content = {
                  groupId: 1,
                  itemId: data.id,
                  user: {
                    id: this.userId,
                    name: this.fullname
                  },
                  type: 3,
                  deploymentId: this.config.deploymentId,
                  action: [
                    {
                      fieldNameRbk: 'lang.word.status',
                      originalValue: data.dossierStatus.name[0]?.name,
                      newValue: 'Công dân yêu cầu rút hồ sơ'
                    }
                  ]
                };
                this.logmanService.postHistory(content).subscribe();
              }
            }
            else {
              this.dialogRef.close(false);
            }
          }, err => {
            this.dialogRef.close(false);
          });
        }
      }
      else {
        let errMsg = "";
        const msgObj = {
          vi: 'Hồ sơ đang được xử lý! Bạn không thể rút hồ sơ này',
          en: 'Dossier is being processed! You can`t withdraw this dossier'
        };
        errMsg = msgObj[localStorage.getItem('language')];
        this.snackbarService.openSnackBar(0, errMsg, '', 'error_notification', this.config.expiredTime);
      }
    }, err => {
      this.dialogRef.close(false);
    });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().replace(/<[^>]*>/g, '').trim().length > this.ckeditorMaxLength) {
        this.isCKMaxLength = true;
      } else {
        this.isCKMaxLength = false;
      }
    } else if (event.editor.getData().replace(/<[^>]*>/g, '').trim().length > 500) {
      this.isCKMaxLength = true;
    } else {
      this.isCKMaxLength = false;
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

  async getDossierStatusSyncToBLDTBXH(dossierData){
    const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
    const UID = localStorage.getItem('UID');
    const user = await this.humanService.getUserInfo3(UID).toPromise();
    this.dossierToSyncStatusBLDTBXH = {
      "MaHoSo": dossierData.code,
      "TaiKhoanXuLy": (!!user?.account?.username && user?.account?.username.length > 0) ? user?.account?.username[0]?.value : "",
      "NguoiXuLy": user?.fullname,
      "ChucDanh": userExperienceAgency?.position?.name,
      "ThoiDiemXuLy": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
      "DonViXuLy": dossierData.agency?.parent?.name[0]?.name,
      "NoiDungXuLy": !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Không có nội dung xử lý",
      "StatusId" : "",
      "NgayBatDau": dossierData.acceptedDate ?
        this.datePipe.transform(dossierData.acceptedDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "NgayKetThucTheoQuyDinh": dossierData.appointmentDate ?
        this.datePipe.transform(dossierData.appointmentDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "UseridCreated": null,
      "UseridEdited": null,
      "DateCreated": null,
      "DateEdited": null
    }
  }

  async syncDossierStatusToBLDTBXH(dossierData){
    await this.getDossierStatusSyncToBLDTBXH(dossierData);
    if(this.dossierToSyncStatusBLDTBXH) {
      this.socialProtectionService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id).then(res => {
        const status = res;

        this.dossierToSyncStatusBLDTBXH.StatusId = status;
        const body = {
          "configId" : this.syncToBLDTBXHConfigId,
          "statusDossier" : this.dossierToSyncStatusBLDTBXH,
        }
        this.adapterService.syncDossierStatusToBLDTBXH(body).subscribe(res => {
          if(res){

          }
        });
      });
    }
  }

  async getDossierStatusSyncKTMToBLDTBXH(dossierData){
    const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
    const UID = localStorage.getItem('UID');
    const user = await this.humanService.getUserInfoKTM(UID).toPromise();
    this.ktmDossierToSyncStatusBLDTBXH = {
      "MaHoSo": dossierData.code,
      "TaiKhoanXuLy": (!!user?.account?.username && user?.account?.username.length > 0) ? user?.account?.username[0]?.value : "",
      "NguoiXuLy": user?.fullname,
      "ChucDanh": userExperienceAgency?.position?.name,
      "ThoiDiemXuLy": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
      "DonViXuLy": dossierData.agency?.parent?.name[0]?.name,
      "NoiDungXuLy": !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Không có nội dung xử lý",
      "StatusId" : "",
      "NgayBatDau": dossierData.acceptedDate ?
        this.datePipe.transform(dossierData.acceptedDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "NgayKetThucTheoQuyDinh": dossierData.appointmentDate ?
        this.datePipe.transform(dossierData.appointmentDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "UseridCreated": null,
      "UseridEdited": null,
      "DateCreated": null,
      "DateEdited": null
    }
  }

  async syncDossierStatusKTMToBLDTBXH(dossierData){
    await this.getDossierStatusSyncKTMToBLDTBXH(dossierData);
    if(this.ktmDossierToSyncStatusBLDTBXH) {
      this.socialProtectionKTMService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id).then(res => {
        const status = res;

        this.ktmDossierToSyncStatusBLDTBXH.StatusId = status;
        const body = {
          "configId" : this.ktmSyncToBLDTBXHConfigId,
          "statusDossier" : this.ktmDossierToSyncStatusBLDTBXH,
        }
        this.adapterService.syncDossierStatusKTMToBLDTBXH(body).subscribe(res => {
          if(res){

          }
        });
      });
    }
  }
  syncDossierKTMToBXD() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant = data?.applicant?.data;
      let thongTinNguoiNop = {
        HoTenNguoiNop: dataApplicant?.fullname, //*
        SoCMND: dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
          + dataApplicant?.village?.label + ","
          + dataApplicant?.district?.label + ","
          + dataApplicant?.province?.label + ","
          + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let hinhThucTraKetQua = 0;
      switch (data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
          hinhThucTraKetQua = 0;
          break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let giayToTrongQuaTrinhXuLy = [];
            // lay ten file va chuyen sang base64
      data.dossierFormFile.forEach((formFile) => {
        let filename;
        formFile.file.forEach((file, index) => {
          filename = file.filename;
          const nameDoc = filename.split(".")[0];
          const extension = filename.split(".").pop();
          const base64Filename = btoa(unescape(encodeURIComponent(filename)));
          giayToTrongQuaTrinhXuLy.push({
            MaGiayTo: null,
            TenGiayTo: nameDoc,
            NoiDungBase64: base64Filename,
            DinhDangTepTin:extension, // dinh dang .pdf, .doc, .png,...
            MoTa: null,
            LoaiGiayTo: "2"
          });
        });
      });

        let maHoSo = data.code
        if(maHoSo.includes("000.00")){
            this.codeMap = maHoSo;
          }else{
            const inputString = maHoSo;
            const parts = inputString.split("-");
            const prefixParts = parts[0].split(".");
            const prefix = `000.00.${prefixParts[1]}.${prefixParts[0]}`;
            this.codeMap = `${prefix}-${parts[1]}-${parts[2]}`;
          }
      let originalString = data.procedure.code;
      let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
      let dossierSync = {
        data: [
          {
            MaHoSo: this.codeMap,
            MaTTHC: result,
            NgayTiepNhan: this.datePipe.transform(new Date(), 'yyyyMMddHHmmss'),
            NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
            TrangThaiHoSo: "7",
            ThongTinNguoiNop: thongTinNguoiNop,
            GiayToTrongQuaTrinhXuLy: giayToTrongQuaTrinhXuLy,
            HinhThucTraKetQua: hinhThucTraKetQua,
            ThongTinTienTrinh: []
          }
        ]
      }
      let config = this.constructConfigId// configId
      console.log(dossierSync)
      this.adapterService.syncDossierConstructKTM(config, dossierSync).subscribe(async data => {
        console.log(data);
      })
    })
  }

  // #region file
  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadedImage = data;
        if (this.commentContent.trim() !== '') {
          var commentTrim = this.commentContent.replace(/&nbsp;/g, '');
          this.postComment(commentTrim);
          resolve(true);
        }
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
        }
        else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }

  getFileIcon(ext) {
    return this.staticUrl + '/icon/files/300x300/' + ext.toLowerCase() + '.png';
  }
  // #endregion

  postComment(commentContent) {
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        fullname: this.fullname
      },
      content: 'Công dân yêu cầu rút hồ sơ. Lý do: ' + commentContent.trim(),
      file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName =  user.username;
      this.humanService.getUserInfo(this.userId).subscribe(data => {
        this.fullname = data.fullname;
      });
    });
  }

  getUserAgencyId(): string {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency !== null) {
      return userAgency?.id;
    } else {
      return this.config?.rootAgency?.id;
    }
  }

  async postCommentAndSendSms(data, withdrawComment: string) {
    let comment = withdrawComment.replace(/<[^>]+>/gi, '');
    let params = {
      parameters: {
        dossierCode: data.code,
        withdrawComment: comment,
      }
    };
    let content = 'Ho so ' + data.code + ' yeu cau rut.\nLy do: ' + comment;
    let smsTemplateId = this.deploymentService.env.OS_HCM.allowOfficerToApproveWithdrawRequest.smsTemplateId;

    // Post comment
    await this.uploadMultiFile(this.files, this.accountId);

    if (smsTemplateId) {
      await this.notiService.getSmsContent(smsTemplateId, params).toPromise().catch(data => {
        if (data.error.text)
          content = data.error.text;
      });
    }

    this.dossierService.sendSmsToOfficerOnWithdraw(this.dossierId, content).subscribe(data => {
      console.log('sms response: ', data);
    });
  }

  checkRequireAttachment(){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
      const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
      if (procedureData?.extendHCM?.requireAttachmentWhenWithdraw) {
        this.requireAttachment = true;
      }
    });
  }

  checkDossierAllowToWithdraw(dossierAgency) {
    if (this.requireAttachment) return true;
    if (this.allowOfficerToApproveWithdrawRequest) {
      let agencyUsed = this.deploymentService.env.OS_HCM.allowOfficerToApproveWithdrawRequest.agencyUsed;
      return tUtils.isAllowedAgency(dossierAgency, agencyUsed);
    } else {
      return false;
    }
  }
  putDossierWithdrawWithComment(){
    const requestBodyObj = {
      comment: '',
      withdrawDate: '',
      user: {
        id: this.userId,
        fullname: this.fullname,
        account: {
          id: this.accountId,
          username: [
            {
              value: this.userName
            }
          ]
        }
      },
      attachment: this.uploadedImage,
      receiveDossier:this.vpcTypeWithDraw
    };
    if (this.commentContent.trim() !== '') {
      var commentTrim = this.commentContent.replace(/&nbsp;/g, '');
      this.postComment(commentTrim);
      requestBodyObj.comment = commentTrim;
    } else {
      const msgObj = {
        vi: 'Rút hồ sơ <b>' + this.dossierCode + '</b> <br /> Lý do:' + requestBodyObj.comment,
        en: 'Dossier <b>' + this.dossierCode + '</b> has been withdrawal <br /> Reason:' + requestBodyObj.comment
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = msgObj[this.selectedLang];
    }
    const msgObjTemp = {
      vi: 'Ngày rút hồ sơ',
      en: 'Withdrawal date'
    };
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.vpcdossierService.putDossierWithdrawWithComment(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
        this.dialogRef.close(true);
        const dataLog = {
          dossierId: this.dossierId,
          code: this.dossierCode,
          body: requestBody
        };
        this.logmanService.postUserEventsLog('withdrawDossier', dataLog).subscribe();
      } else {
        this.dialogRef.close(false);
      }
    }, err => {
      this.dialogRef.close(false);
    });
  }
  vpcUploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadedImage = data;
        this.putDossierWithdrawWithComment();
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }
  changeCheckRutHSG(event){
    // if(type.value == 0) {
    //   this.hide_CSDLDC = true;
    //   this.hide_DVCQG = false;
    // } else if(type.value == 1) {
    //   this.hide_CSDLDC = false;
    //   this.hide_DVCQG = true;
    // }
    this.vpcTypeWithDraw = event.value;
  }

}
export class ConfirmWithdrawDialogModel {
  constructor(public id: string, public dossierCode: string) {
  }
}
