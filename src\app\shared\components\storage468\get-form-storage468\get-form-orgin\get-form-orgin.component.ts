import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {IFormOrginInFoOutput} from "shared/components/storage468/storage468.schema";
import {Storage468Service} from "shared/components/storage468/storage468.service";

@Component({
  selector: 'get-form-orgin',
  templateUrl: './get-form-orgin.component.html',
  styleUrls: ['./get-form-orgin.component.scss']
})
export class GetFormOrginComponent{
  @Input() fullname:string;
  @Input() identityNumber:string;
  @Input() birthday:string;
  @Input() tabIndex = 1;
  public formOrginIds:string[] = undefined;
  public mapedIds: any;
  public forms?:IFormOrginInFoOutput[] = undefined;
  public electronicVersionId = '6204d0a679894379eae831c5';
  private oldFullname:string;
  private oldIdentityNumber:string;
  private oldBirthday:string;
  public found = false;

  constructor(private storage468Service: Storage468Service) {
  }

  getDDMMYYYY(isoDate){
    if(isoDate == '' || isoDate == null) return '';
    let date = new Date(isoDate);
    let year = date.getFullYear();
    let month = date.getMonth()+1;
    let day = date.getDate();
    let mm = `${month}`;
    let dd = `${day}`

    if (day < 10) {
      dd = `0${day}`
    }
    if (month < 10) {
      mm = `0${month}`;
    }
    return `${dd}/${mm}/${year}`;
  }

  onLoad($event){
    if($event.index===this.tabIndex
      && this.fullname && this.identityNumber && this.birthday
      && (this.fullname !== this.oldFullname || this.identityNumber !== this.oldIdentityNumber || this.birthday !== this.oldBirthday)
      ){
      this.found = false;
      this.formOrginIds = this.mapedIds.filter(i=>i.storageid).map(i=>i.storageid);
      this.storage468Service.getFormOrginIns(this.formOrginIds,this.identityNumber,this.fullname,this.getDDMMYYYY(this.birthday)).subscribe(rs=>{
        this.forms = rs;
        this.formOrginIds = this.forms.map(i=>i?.formOrgin.id);
        this.oldFullname = this.fullname;
        this.oldIdentityNumber = this.identityNumber;
        this.oldBirthday = this.oldBirthday;
        this.found = true;
      });
    }
  }

  pushFormOrginIds(formOrginIds:string[]){
    this.formOrginIds = formOrginIds;
  }

  pushMapedIds(mapedIds){
    this.mapedIds = mapedIds;
  }
}
