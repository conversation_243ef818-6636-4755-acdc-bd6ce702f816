import { Injectable } from '@angular/core';
import { NativeDateAdapter } from '@angular/material/core';
import * as tUtils from 'src/app/data/service/thoai.service';

@Injectable()
export class CustomDateAdapter extends NativeDateAdapter {
    parse(value: any): Date | null {

        if ((typeof value === 'string') && (value.indexOf('/') > -1)) {
            const str = value.split('/');

            const year = Number(str[2]);
            const month = Number(str[1]) - 1;
            const date = Number(str[0]);

            return new Date(year, month, date);
        } else {
            try {
                const isDateValid = new Date(value).getDate();
                if (isNaN(isDateValid) && value !== '') {
                    const msgObj = {
                        vi: 'Vui lòng nhập đúng định dạng!',
                        en: 'Please enter the correct format!'
                    };
                    // tslint:disable-next-line: radix
                    if (isNaN(parseInt(value))) {
                        alert(msgObj[localStorage.getItem('language')]);
                    }
                    return tUtils.newDate();
                } else {
                    const timestamp = typeof value === 'number' ? value : Date.parse(value);
                    return isNaN(timestamp) ? null : new Date(timestamp);
                }
            } catch (error) {
            }
        }

    }

    format(date: Date, displayFormat: object): string {
        date = new Date(Date.UTC(
            date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(),
            date.getMinutes(), date.getSeconds(), date.getMilliseconds()));
        displayFormat = Object.assign({}, displayFormat, { timeZone: 'utc' });

        const dtf = new Intl.DateTimeFormat(this.locale, displayFormat);
        return dtf.format(date).replace(/[\u200e\u200f]/g, '');
    }

}
