::ng-deep {
    
    .digoSelectBox {

        .w100 {
            width: 100%;
        }

        .mat-form-field-appearance-outline .mat-form-field-outline {
            color: transparent;
        }

        .mat-form-field-appearance-outline .mat-form-field-outline {
            color: transparent;
        }

        .mat-form-field-appearance-outline .mat-form-field-outline {
            background-color: #eaebeb;
            border-radius: 5px;
        }

        .mat-form-field-autofill-control {
            background-color: #eaebeb;
        }

        .mat-form-field-appearance-outline .mat-form-field-infix {
            padding: 0.8em 0;
        }

        .mat-form-field-label-wrapper {
            top: -1em;
        }

        .mat-form-field.mat-focused .mat-form-field-label {
            color: #ce7a58;
            margin-bottom: 1em;
        }

        .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
        .mat-form-field-label {
            color: #ce7a58;
            margin-bottom: 1em;
        }
    }
}

::ng-deep .searchNested {
    width: 80%;
    height: 50px;
    margin-left: 15px;
}

::ng-deep .clearSearchNested {
    position: absolute;
    top: 0;
    right: 0;
}

::ng-deep .spinnerBtn {
    position: absolute;
    top: 0;
    right: 2rem;
}

::ng-deep .listSpinner {
    width: 20px !important;
    height: 20px !important;
}

::ng-deep .listSpinner ::ng-deep svg {
    width: 20px !important;
    height: 20px !important;
}