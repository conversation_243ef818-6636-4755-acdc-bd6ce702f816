export class CaptchaWithoutO0 {
    font: any;
    align: any;
    baseline: any;
    width: any;
    height: any;
    color: any;
    bgColor: any;
    length: any;
    createImage: () => string;
    constructor(font, align, baseline, width, height, bgColor, color, length) {
        this.font = font;
        this.align = align;
        this.baseline = baseline;
        this.width = width;
        this.height = height;
        this.color = color;
        this.bgColor = bgColor;
        this.length = length;
        this.createImage = () => {
            const captcha = document.createElement('canvas');
            const newImage = document.createElement('image');

            const getRandomInt = (min, max) => {
                min = Math.ceil(min);
                max = Math.floor(max);
                return Math.floor(Math.random() * (max - min)) + min;
            };

            const getRandomIntExclude = (min, max, exclude) => {
                let randomNum;
                do {
                    min = Math.ceil(min);
                    max = Math.floor(max);
                    randomNum = Math.floor(Math.random() * (max - min)) + min;
                } while (exclude !== null && randomNum === exclude);
                return randomNum;
            };

            const getCharacterWithoutO0 = () => {
                ////debugger
                const arr = [];
                arr.push(getRandomIntExclude(97, 122, 111));
                arr.push(getRandomIntExclude(65, 90, 79));
                arr.push(getRandomInt(49, 57));
                const value = arr[getRandomInt(0, arr.length)];
                return String.fromCharCode(value);
            };

            const getWord = () => {
                ////debugger
                // tslint:disable-next-line: no-shadowed-variable
                let newWord = ``;
                let count = 0;
                while (count < this.length) {
                    newWord += getCharacterWithoutO0();
                    count++;
                }
                return newWord;
            };

            const newWord = getWord();
            ////debugger
            const context = captcha.getContext('2d');
            context.canvas.width = this.width;
            context.canvas.height = this.height;
            context.font = this.font;
            context.textAlign = this.align;
            context.textBaseline = this.baseline;
            context.fillStyle = this.bgColor;
            context.fillRect(0, 0, this.width, this.height);
            context.fillStyle = this.color;
            context.fillText(newWord, this.width / 2, this.height / 2);
            newImage.setAttribute('src', context.canvas.toDataURL());
            newImage.setAttribute('data-key', newWord);
            newImage.setAttribute('style', 'height: 46px');
            return newImage.outerHTML;
        };
    }
}