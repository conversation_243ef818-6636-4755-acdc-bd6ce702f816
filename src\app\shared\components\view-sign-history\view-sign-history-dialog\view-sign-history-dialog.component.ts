import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'view-sign-history-dialog',
  templateUrl: './view-sign-history-dialog.component.html',
  styleUrls: ['./view-sign-history-dialog.component.scss']
})
export class ViewSignHistoryDialogComponent implements OnInit {
  displayedColumnsDefault: string[] = ['signer', 'email', 'issuer', 'signingTime'];
  displayedColumnsHCM: string[] = ['signer', 'email', 'issuer', 'signingTime', 'contentValidInfo'];
  
  //IGATESUPP-32441-phucnh.it2
  enableContentValidInfo = this.deploymentService.env?.OS_HCM?.enableContentValidInfo;  
  displayedColumns: string[] = this.enableContentValidInfo == 1 ? this.displayedColumnsHCM : this.displayedColumnsDefault;
  constructor(
    public dialogRef: MatDialogRef<ViewSignHistoryDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private deploymentService: DeploymentService,
  ) {}

  ngOnInit(): void {
    console.log(this.data);    
  }
  closeDialog(){
    this.dialogRef.close();
  }
 
 
}
