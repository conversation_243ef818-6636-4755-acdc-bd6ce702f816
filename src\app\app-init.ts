import { KeycloakService, KeycloakOptions } from 'keycloak-angular';
import { EnvService } from './core/service/env.service';

export function initializer(keycloakService: KeycloakService, envService: EnvService): () => Promise<any> {
  return (): Promise<any> => {
    return new Promise<any>((resolve, reject) => {
      envService.loadConfig().then((config) => {
        const keycloakOptions = config.keycloakOptions as KeycloakOptions;
        // fix lỗi khi chạy local hong hiển thị trang dvc màu trắng
        if (window.location.hostname !== 'localhost'){
          const path = window.location.pathname.split('/');
          if (path && path.length > 1) {
            // FIXME gan tam vi cho first path
            let firstPath = path[1];
            if (firstPath !== 'vi' && firstPath !== 'en'){
              firstPath = 'vi';
            }
            keycloakOptions.initOptions.silentCheckSsoRedirectUri = `${window.location.origin}/${firstPath}/assets/silent-check-sso.html`;
          }
        }
        return keycloakService.init(keycloakOptions).then(() => {
          resolve(config);
        }).catch(() => {
          reject('Failed to initialize keycloak!');
        });
      });
    });
  };
}
