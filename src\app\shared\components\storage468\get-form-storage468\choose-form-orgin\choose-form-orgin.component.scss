.dialog_title {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  color: #CE7A58;
}

::ng-deep .addForm .mat-form-field-wrapper {
  padding-bottom: 2px;
}

::ng-deep .addBtn {
  margin-top: 2.5em;
  background-color: #CE7A58;
  color: #fff;
  height: 3em;

}

.close-button {
  float: right;
  top: -24px;
  right: -24px;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #e8e8e8;
}

.diagram-container {
  height: 300px;
}

.full-width-dialog .mat-dialog-container {
  max-width: 100vw !important;
}

.btn_upload {
  color: #1e2f41!important;
  width: 100%;
  cursor: pointer;
  margin: 0.5em 0;
  background-color:transparent;
  mat-icon{
    color: #ce7a58;
  }
  .text{
    font-size: 12px;
    font-weight: initial;
  }
}

.btn_acction{
  mat-icon{
    color: #4CA2DD;
  }
}

table th, table td{
  text-align: center;
}

.btn_upload:hover{
  background-color: #FFFFFF;
}
