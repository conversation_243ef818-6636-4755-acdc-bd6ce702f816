import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { EnvService } from 'src/app/core/service/env.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-view-process-diagram',
  templateUrl: './view-process-diagram.component.html',
  styleUrls: ['./view-process-diagram.component.scss']
})
export class ViewProcessDiagramComponent implements OnInit {

  config = this.envService.getConfig();
  processId: string;
  processName: string;
  activitiId: string;
  bpmProcessDefinitionId: string;
  diagramUrl = '';
  checkOneGateAdminMaster = Number(sessionStorage.getItem('oneGateAdminMaster')) || 0;
  checkOneGateProcessCatalog = Number(sessionStorage.getItem('oneGateProcessCatalog')) || 0;

  constructor(
    public dialogRef: MatDialogRef<ViewProcessDiagramComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ViewProcessDiagramDialogModel,
    private procedureService: ProcedureService,
    private envService: EnvService,
    private router: Router,
  ) {
    this.activitiId = data.activitiId;
    this.processName = data.processName;
    this.bpmProcessDefinitionId = data.bpmProcessDefinitionId;
  }

  ngOnInit(): void {
    this.getModel();
  }

  onDismiss() {
    const result = {
      name: this.processName,
      status: null
    };
    this.dialogRef.close(result);
  }

  // ================================================= Manual Function
  getModel() {
    this.diagramUrl = this.procedureService.getUrlModel(this.activitiId);
  }

  print() {
    (document.querySelector('.btn-control') as HTMLElement).style.display = 'none';
    const divToPrint = document.getElementById('print-section');
    const newWin = window.open('', 'Print-Window');
    newWin.document.open();
    // tslint:disable-next-line: max-line-length
    newWin.document.write('<html><title>Một cửa điện tử - ' + this.processName + '</title><body onload="window.print()">' + divToPrint.innerHTML + '</body></html>');
    newWin.document.close();
    setTimeout(() => {
      newWin.close();
    }, 1);
    (document.querySelector('.btn-control') as HTMLElement).style.display = 'grid';
  }

  processConfig() {
    this.router.navigate(['/onegate-data/list-process/config/' + this.bpmProcessDefinitionId]);
    this.dialogRef.close(null);
  }

}

export class ViewProcessDiagramDialogModel {
  constructor(public activitiId: string, public processName: string, public bpmProcessDefinitionId: string) { }
}
