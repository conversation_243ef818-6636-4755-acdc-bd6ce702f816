<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'dialog_title_new' : 'dialog_title'" mat-dialog-title><span >{{processButtonName ? processButtonName + ":" : "Quy trình hồ sơ:"}} </span>{{dossierDetail?.code}}</h3>
<div mat-dialog-content class="processHandleDialogContent">
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center" fxLayoutAlign="space-between">
      <div class="imgProcess" fxFlex='49.5'>
          <div style="height: 20vh">
              <div class="card">
                  <div class="cardTitle">
                      <span *ngIf="processName != undefined && processName != null && processName != ''" class="processName">
                          <span >Quy trình: </span> {{processName}}
                      </span>
                  </div>
                  <div class="cardContent" *ngIf="!isShowHGI">
                        <span style="display: block" *ngIf="showAppointmentDateProcess == 1 || !dossierTaskStatusNew">
                          <span>Ngày hẹn trả: </span>
                          <span *ngIf="(dossierDetail?.appointmentDate === null || dossierDetail?.appointmentDate === undefined) && !(dossierDetail.dossierStatus.id == 0 && dossierDetail.applyMethod.id == 0)">{{timesheet?.endDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                          <span *ngIf="dossierDetail?.appointmentDate !== null && dossierDetail?.appointmentDate !== undefined">{{dossierDetail?.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                      </span>
                      <span style="display: block" *ngIf="(showEndDateProcess == 0 || !dossierTaskStatusNew) && !(dossierDetail.dossierStatus.id == 0 && dossierDetail.applyMethod.id == 0)">Hạn xử lý toàn quy trình: {{timesheet?.endDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                      <span style="display: block" *ngIf="showEndDateProcess == 2 && dossierTaskStatusNew">Hạn xử lý toàn quy trình: {{endData}}</span>
                      <span style="display: block">Hình thức tiếp nhận: {{dossierDetail?.applyMethod?.name}}</span>
                      <span style="display: block">Hình thức nhận kết quả: {{dossierDetail?.dossierReceivingKind?.name}}</span>
                  </div>
                  <div class="cardContent" *ngIf="isShowHGI">
                    <span style="display: block" *ngIf="(showEndDateProcess == 0 || !dossierTaskStatusNew) && isShowHGI">Ngày nộp hồ sơ: {{this.dossierDetail?.appliedDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                    <span style="display: block" *ngIf="showAppointmentDateProcess == 1 || !dossierTaskStatusNew">
                      <span>Ngày hẹn trả: </span>
                      <span *ngIf="(dossierDetail?.appointmentDate === null || dossierDetail?.appointmentDate === undefined) && isShowHGI">{{getNameDossierStatusHGI(dossierDetail?.dossierStatus?.name)}}</span>
                      <span *ngIf="(dossierDetail?.appointmentDate !== null && dossierDetail?.appointmentDate !== undefined) && isShowHGI">{{dossierDetail?.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                    </span>
                  <span style="display: block" *ngIf="showEndDateProcess == 2 && dossierTaskStatusNew">Hạn xử lý toàn quy trình: {{endData}}</span>
                  <span style="display: block">Hình thức tiếp nhận: {{dossierDetail?.applyMethod?.name}}</span>
                  <span style="display: block">Hình thức nhận kết quả: {{dossierDetail?.dossierReceivingKind?.name}}</span>
              </div>
              </div>
          </div>
          <app-process-diagram *ngIf="!!diagramUrl && !!taskId && !configViewProcessingKHA" class="diagramViewer" [url]="diagramUrl" ></app-process-diagram>
          <div *ngIf="configViewStepByStepProcessKHA && processSteps.length > 0" class="step-by-step-table">
                <table class="process-steps-table mat-elevation-z2">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Tên bước</th>
                            <th>Cơ quan thực hiện</th>
                            <th>Thời gian thực hiện</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let step of processSteps; let i = index">
                            <td class="center-align">{{i + 1}}</td>
                            <td>{{step.activiti.name}}</td>
                            <td>
                                <span *ngIf="step.candidateGroup && step.candidateGroup.length > 0">
                                    {{step.candidateGroup[0]?.name[0]?.name || ''}}
                                </span>
                                <span *ngIf="!step.candidateGroup || step.candidateGroup.length === 0"></span>
                            </td>
                            <td class="center-align">{{step.processingTime}} {{step.formattedTimeUnit}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
      </div>
      <div class="taskProcess" fxFlex='49.5'>
          <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between">
              <div class="card" *ngFor='let task of tasks'>
                  <div class="cardTitle">
                    <span *ngIf="!isDisplayStep">Bước: {{task.bpmProcessDefinitionTask.definitionTask.name}}
                      <span style="font-size: 15px !important;">
                          <p style="margin: 0 !important"><mat-icon>business</mat-icon> <span>{{task.agencyName}}</span></p>
                          <p style="margin: 0 !important; font-style: italic">({{task.bpmProcessDefinitionTask.processingTime}}
                              <span *ngIf="task.bpmProcessDefinitionTask.processingTimeUnit === 'd'">ngày</span>
                              <span *ngIf="task.bpmProcessDefinitionTask.processingTimeUnit === 'h'">giờ</span>
                              <span *ngIf="task.bpmProcessDefinitionTask.processingTimeUnit === 'm'">phút</span>
                              làm việc)</p>
                      </span>
                  </span>
                  <span *ngIf="isDisplayStep">{{task.bpmProcessDefinitionTask.definitionTask.name}}
                    <span style="font-size: 15px !important;">
                        <p style="margin: 0 !important"><mat-icon>business</mat-icon> <span>{{task.agencyName}}</span></p>
                        <p style="margin: 0 !important; font-style: italic">({{task.bpmProcessDefinitionTask.processingTime}}
                            <span *ngIf="task.bpmProcessDefinitionTask.processingTimeUnit === 'd'">ngày</span>
                            <span *ngIf="task.bpmProcessDefinitionTask.processingTimeUnit === 'h'">giờ</span>
                            <span *ngIf="task.bpmProcessDefinitionTask.processingTimeUnit === 'm'">phút</span>
                            làm việc)</p>
                    </span>
                </span>

                  </div>
                  <div class="cardContent">
                    <div *ngIf="env?.OS_HCM?.showAssigneeName">
                      <p>
                          <mat-icon>move_to_inbox</mat-icon><span class="lbl">Người xử lý: </span>
                          <span *ngIf="!!task.assignee && !!task.assignee.id">{{task.assignee.fullname}}</span>
                                <span *ngIf="(!task.assignee || !task.assignee.id) && task.candidateUser?.length !== 0">
                                    <span *ngFor="let user of task.candidateUser; let i = index;">
                                        <span>{{user.fullname}}</span>
                                        <span *ngIf="i !== task.candidateUser.length - 1">, </span>
                                    </span>
                                </span>
                                <span *ngIf="task.assignee === null && task.candidateUser?.length === 0 && task.candidateGroup?.length !== 0">
                                    {{task.agencyName}}
                                </span>
                      </p>
                      <p *ngIf="!!task.candidatePosition?.name && task.candidatePosition?.name?.length > 0">
                        <mat-icon>person_pin</mat-icon><span class="lbl">Chức vụ: </span>
                        <span *ngFor="let position of task.candidatePosition.name; let i = index;">
                            <span>{{position.name}}</span>
                            <span *ngIf="i !== task.candidatePosition.name.length - 1">, </span>
                        </span>
                      </p>
                    </div>
                      <p *ngIf="task.createdDate !== undefined && task.createdDate !== null">
                          <mat-icon>more_time</mat-icon><span class="lbl">Ngày tiếp nhận: </span>
                          {{task.createdDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                      </p>
                      <p *ngIf="task?.activitiTask?.status === 'COMPLETED'">
                          <mat-icon>update</mat-icon><span class="lbl">Ngày chuyển hồ sơ: </span>
                          {{task.updatedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                      </p>
                      <p>
                          <mat-icon>event_available</mat-icon><span class="lbl">Hạn xử lý: </span>
                          {{task.dueDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                      </p>
                  </div>
              </div>
          </div>
      </div>
  </div>

</div>
