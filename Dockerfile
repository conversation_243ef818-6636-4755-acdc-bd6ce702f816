FROM nginx:stable-alpine

USER root

RUN apk --update add bash && \
    apk add dos2unix

COPY nginx.conf /etc/nginx/nginx.conf
COPY docker-entrypoint.sh /

RUN dos2unix /docker-entrypoint.sh

RUN chmod 777 -R /docker-entrypoint.sh

WORKDIR /usr/share/nginx/html
COPY dist/web .

COPY robots.txt /usr/share/nginx/html/vi/
COPY sitemap.xml /usr/share/nginx/html/vi/
COPY google9e23b609d40442e9.html /usr/share/nginx/html/vi/

ENTRYPOINT [ "/docker-entrypoint.sh" ]