table {
  width: 100%;
  .mat-cell{
    padding-right: 5px;
  }
}
@media screen and (max-width: 1279px) {
  .table-container {
    .table-holder {
      table {
        .mat-header-row {
          display: none;
        }
        .mat-row {
          width: 100%;
          height: 100%;
          display: block;
          border: 1px solid #ccc;
          border-left: none;
          border-right: none;
          td {
            display: block;
            border: none;
            border-bottom: 1px solid #eee;
            position: relative;
            padding: 0.5rem 0 0.5rem 40%;
          }
          td:before {
            position: absolute;
            left: 0.5rem;
            font-weight: 500;
            color: #1e2f41;
          }
          td:nth-of-type(1):before {
            content: "STT";
          }
          td:nth-of-type(2):before {
            content: "Mã PAKN";
          }
          // td:nth-of-type(3):before {
          //   content: "SĐT";
          // }
          td:nth-of-type(2):before {
            content: "Tiêu đề";
          }
          td:nth-of-type(3):before {
            content: "<PERSON><PERSON><PERSON><PERSON> mục";
          }
          td:nth-of-type(4):before {
            content: "<PERSON>ày phản ánh";
          }
          td:nth-of-type(5):before {
            content: "Địa điểm phản ánh";
          }
          td:nth-of-type(6):before {
            content: "Trạng thái";
          }
          td:nth-of-type(7):before {
            content: "Tiến độ";
          }
          td:nth-of-type(8):before {
            content: "Thao tác";
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1279px) {
  .table-holder {
    .mat-paginator-outer-container {
      .mat-paginator-container {
        flex-direction: column;
        font-size: 14px;
        .mat-paginator-range-actions {
          .mat-paginator-range-label {
            margin: 0;
            margin-right: 1rem;
          }
        }
      }
    }
    table {
      .mat-row {
        td {
          padding-left: 62% !important;
        }
      }
    }
    .mat-paginator-range-label {
      font-size: 11px;
    }
  }
}
