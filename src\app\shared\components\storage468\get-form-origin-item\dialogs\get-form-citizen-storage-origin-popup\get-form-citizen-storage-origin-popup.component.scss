::ng-deep .all-content{
  width: 100%;
  ::ng-deep .close-button{
    float: right;
    width: 40px;
    height: 49px;
    border-radius: 0px;
    background-color: #ffffff;
  }
  iframe{
    height: 85vh;
    border: none;
  }
  .procedureName{
    font-size: 17px;
    line-height: 27px;
    color: #ce7a58;
    padding-left: 1%;
  }
  .tabs {
    background-color: #CCCCCC;
    margin: 0px 0px 20px;
  }
}
::ng-deep .icon-button-calendar {
  height: 42.5px;
  width: 42px;
  background-color: #E9ECEF;
  border-color: #CED4DA;
  border-width: 1px;
  border-left-width: 0px;
  border-radius: 0px 4px 4px 0px;
  border-style: solid;
  margin-bottom: 10px;
  //margin-top: 12px;
}
::ng-deep .icon-button-calendar i {
  color: #666666;
  font-size: 16px;
}
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
button.icon-button-calendar:hover {
  background-color: #e0e0e0;
}
button.icon-button-calendar:active {
  background-color: #d6d6d6;
}
button.error-button {
  border: 3px solid red !important;
  background-color: #d6d6d6 !important;
}
.has-error {
  background: #f8d7da;
  border-radius: 5px;
  padding: 10px;
}
.has-error input,
.has-error mat-form-field {
  border: 1px solid red;
}
::ng-deep .custom-tooltip {
  background-color: #ff4444 !important;
  color: white !important;
  font-size: 14px !important;
  padding: 10px !important;
  border-radius: 5px !important;
  text-align: center;
}
.form-flex {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  .form-row.full-width {
    display: flex;
    width: 100%;
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .form-row.two-columns {
    display: flex;
    width: 100%;
    gap: 10px;
    margin-bottom: 20px;
    padding-top: 0px;
  }
  .form-item {
    flex: 1;
  }
  .date-picker-container {
    display: flex;
    align-items: center;
    width: 100%;
  }
    .form-control {
      padding: 0 6px;
      border-color: #D9D9D9;
      border-radius: 4px;
      border-width: 1px;
      border-style: solid;
      box-shadow: #555555;
      height: 40px;
      font-size: 16px;
      color: #1E2F41;
      width: 85%;
      background-color: #fff;
      transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 0px;
      placeholder {
        font-weight: normal;
      }
      focus {
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(206, 122, 88, 0.4);
      }
      &.error {
        border-color: #262121;
        background-color: #ffdddd;
      }
      .error-text {
        color: red;
        font-size: 14px;
        margin-top: 4px;
      }
    }
    .date-input {
      width: 100%;
    }

    .btn-search {
      background-color: #ce7a58;
      color: white;
      height: 42px !important;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      width: 150px;
      margin-bottom: 8px;
      &:disabled {
        background-color: #d6d4d4;
      }
    }
    .btn-others {
      background-color: #ce7a58;
      color: white;
      height: 42px !important;
      border: none;
      border-radius: 4px;
      width: 250px;
      margin-bottom: 0px;
      justify-content: center !important;
      align-items: end;
      margin-top: 15px;
      &:disabled {
        background-color: #d6d4d4;
      }
  }
  .search-btn {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    min-width: 150px;
  }
}
.margin-error {
  margin-bottom: 28px !important;
}

.error-select {
  border: 1px solid red !important;
  border-radius: 0px;
  box-shadow: 0 0 5px red !important;
}
::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper {
  height: 45px !important;
}
::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  height: 100% !important;
  align-items: center;
}
::ng-deep .mat-select-trigger {
  height: 100% !important;
}
::ng-deep .mat-select-value {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: unset !important;
  max-width: none !important;
}
::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
  height: 45px !important;
  border-color: #80BDFF !important;
  box-shadow: 0 0 5px #80BDFF !important;
  background-color: transparent !important;
  border-radius: 4px;
}
::ng-deep .mat-form-field-appearance-outline.invalid-field .mat-form-field-outline {
  border: 1px solid red !important;
  border-radius: 4px !important;
  box-shadow: 0 0 5px red !important;
  background-color: #ffcccc !important;
}
.custom-combobox {
  padding-top: 0px;
  font-size: 15px;
  max-height: 40px;
}

::ng-deep .custom-mat-input {
  height: 28px !important;
  font-size: 15px;
  color: #1E2F41;
  width: 95%;
  background-color: #fff;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  padding: 6px 9px;
  margin-bottom: 7px;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

::ng-deep .custom-mat-input:focus {
  border-color: #3F51B5 !important;
  box-shadow: 0 0 4px #80BDFF !important;
}

::ng-deep .custom-mat-input.error-border {
  border: 1px solid red !important;
  box-shadow: 0 0 5px red !important;
  background-color: #ffcccc !important;
}

::ng-deep .custom-mat-input-calendar {
  border-color: #D9D9D9;
  border-radius: 4px 0px 0px 4px;
  border-width: 1px;
  border-style: solid;
  box-shadow: #555555;
  height: 40px;
  font-size: 15px;
  color: #1E2F41;
  width: 85%;
  background-color: #fff;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 0px;
  margin-bottom: 7px;
  padding: 0 6px 0 10px;
}

::ng-deep .custom-mat-input-calendar:focus {
  border-color: #3F51B5 !important;
  box-shadow: 0 0 4px #80BDFF !important;
}

::ng-deep .custom-mat-input-calendar.error-border {
  border: 1px solid red !important;
  box-shadow: 0 0 5px red !important;
  background-color: #ffcccc !important;
}

::ng-deep .error-message-combobox {
  color: red !important;
  padding-top: 0px;
  font-size: 16px;
  margin-left: 10px !important;
  margin-top: 14px;
}
::ng-deep .error-message {
  color: red !important;
  padding-top: 0px;
  font-size: 16px;
  margin-left: 10px !important;
}

mat-form-field {
  height: 100px;
  width: 100%;
  padding-top: 0px;
  margin-top: -5px;
  margin-bottom: -5px;
}
.mat-select {
  border: none !important;
  outline: none !important;
}
.container-button {
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  display: flex;
}
.color-title {
  color: #000000;
  font-size: 16px;
  padding-bottom: 0px;
}
.required {
  color: red;
  font-size: 1rem;
}
.ho-so-widget {
  display: flex;
  flex-direction: column;
  width: 100%;
  mat-card {
    padding: 12px;
    border-radius: 0px;
    background-color: #bbbbbb;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    mat-card-content {
      flex-direction: column;
      .info-row {
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #333;
        label {
          color: #666;
        }
      }
    }
  }
}

.container {
  width: 100%;
  margin: 0;
  padding: 0;
}
.table-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
}
table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin-top: -5px;
  text-align: center;
  font-size: 16px;
}
th:nth-child(1), td:nth-child(1) {
  width: 5%;
  padding-left: 0;
}
th:nth-child(4), td:nth-child(4) {
  width: 15%;
}
th:nth-child(5), td:nth-child(5) {
  width: 10%;
}
th:nth-child(2), td:nth-child(2),
th:nth-child(3), td:nth-child(3) {
  width: 35%;
}
th {
  font-size: 16px;
  color: #000;
  padding: 12px;
  text-align: center;
}
td {
  padding: 12px;
  text-align: center;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
tr.mat-header-row {
  background-color: #ffffff !important;
  height: 60px;
}
.stt-cell,
.maHoSo-cell,
th,
td {
  text-align: center;
}
.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}
.icon {
  margin-left: -5px;
  font-size: 15px;
  color: #007aff;
  display: flex;
  justify-content: center;
  align-items: center;
}
::ng-deep .mat-checkbox-checked .mat-checkbox-background {
  background-color: #ce7a58 !important;
}
.info-container {
  display: grid;
  grid-template-columns: 150px auto;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 10px;
}
.info-label {
  text-align: left;
  white-space: nowrap;
  font-size: 16px;
}
.info-content {
  text-align: left;
  word-break: break-word;
  font-style: italic;
  font-weight: bold;
}

::ng-deep .custom-label {
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

::ng-deep .required {
  color: red;
  font-size: 16px;
}
.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 30px;
  text-align: center;
}
.init-label {
  font-size: 16px;
  color: rgba(116, 116, 116, 0.91);
}
.warning-label {
  font-size: 16px;
  color: #fd8f8f;
}
.custom-color{
  background-color: #fd8f8f;
  width: auto;
}
.btnUploadFile {
  background-color: #ce7a58;
  color: white;
  margin-top: 3em;
  margin-bottom: 2em;
  height: 2.5em !important;
  }
.sppName{
    font-weight: 500;
    font-size: 17px;
    line-height: 27px;
    color: #ce7a58;
    padding-left: 20px;
}