<div fxLayout="row" fxLayoutAlign="space-between">
  <h2><PERSON><PERSON>ch sử ký số</h2>
  <button mat-icon-button class="close-button-icon" (click)="closeDialog()">
    <mat-icon>close</mat-icon>
  </button>
</div>
<div>
  <table mat-table matSort [dataSource]="data"  class="mat-elevation-z4 all-petition">

    <ng-container matColumnDef="signer">
      <th mat-header-cell *matHeaderCellDef [ngStyle.gt-md]="{'width.%': '25', 'text-align': 'center'}">Ng<PERSON><PERSON><PERSON> ký</th>
      <td mat-cell *matCellDef="let element" style="text-align: center">{{ element.signer }}</td>
    </ng-container>

    <ng-container matColumnDef="email">
      <th mat-header-cell *matHeaderCellDef [ngStyle.gt-md]="{'width.%': '25', 'text-align': 'center'}">Email</th>
      <td mat-cell *matCellDef="let element" style="text-align: center">{{element.email}}</td>
    </ng-container>

    <ng-container matColumnDef="issuer">
      <th mat-header-cell *matHeaderCellDef [ngStyle.gt-md]="{'width.%': '25', 'text-align': 'center'}">Cơ quan chứng thực</th>
      <td mat-cell *matCellDef="let element" style="text-align: center">{{element.issuer}}</td>
    </ng-container>

    <ng-container matColumnDef="signingTime">
      <th mat-header-cell *matHeaderCellDef [ngStyle.gt-md]="{'width.%': '25', 'text-align': 'center'}">Ngày ký</th>
      <td mat-cell *matCellDef="let element" style="text-align: center">{{element.signingTime | date: 'dd/MM/yyyy HH:mm:ss'}}</td>
    </ng-container>

    <ng-container matColumnDef="contentValidInfo" *ngIf="enableContentValidInfo == 1">
      <th mat-header-cell *matHeaderCellDef [ngStyle.gt-md]="{'width.%': '25', 'text-align': 'center'}">Thông tin hiệu lực</th>
      <td mat-cell *matCellDef="let element" style="text-align: center">{{(element.contentValidInfo == 'valid') ? 'Hợp lệ' : 'Không hợp lệ'}}</td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
</div>
