<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>{{data.title}}</h3>
<form [formGroup]="saveForm" class="addForm">
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex='grow'>
      <mat-label>CMDD/CCCD</mat-label>
      <input type="text" matInput formControlName="identityNumber" readonly>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex='grow'>
      <mat-label>Họ tên công dân</mat-label>
      <input type="text" matInput formControlName="fullname" readonly>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex='grow'>
      <mat-label>Ngày sinh</mat-label>
      <input type="text" matInput formControlName="birthday" readonly>
    </mat-form-field>
  </div>
  <div fxLayout="row" *ngIf="data.docIdentityName" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex='grow'>
      <mat-label>{{data.docIdentityName}}</mat-label>
      <input type="text" matInput formControlName="identityDoc" required>
    </mat-form-field>
  </div>
  <div fxLayout="row" *ngIf="data.formOrginInfo.expire == true" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex='grow'>
      <mat-label>Ngày hết hạn</mat-label>
      <input matInput [matDatepicker]="pickerExpireDay" formControlName="expireDay" (dateChange)="changeExpireDay()">
      <mat-datepicker-toggle matSuffix [for]="pickerExpireDay"></mat-datepicker-toggle>
      <mat-datepicker #pickerExpireDay></mat-datepicker>
    </mat-form-field>
  </div>
  <div fxLayout="row" *ngIf="data.formOrginInfo.expire == true" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex.gt-md="50" fxFlex.gt-sm="50" fxFlex='grow'>
      <mat-label>Ngày hiệu lực</mat-label>
      <input matInput [matDatepicker]="pickerEffectiveDay" formControlName="effectiveDay" (dateChange)="clearExpireDay()">
      <mat-datepicker-toggle matSuffix [for]="pickerEffectiveDay"></mat-datepicker-toggle>
      <mat-datepicker #pickerEffectiveDay></mat-datepicker>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex='grow'>
      <mat-label>Thời gian hiệu lực</mat-label>
      <input type="text" matInput (keyup)="clearExpireDay()" formControlName="txtEffectiveDay" onlynumber>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex='grow'>
      <mat-label>Đơn vị</mat-label>
      <mat-select formControlName="cboTypeTime" (selectionChange)="clearExpireDay()" msInfiniteScroll>
        <mat-option value="D">Ngày</mat-option>
        <mat-option value="M">Tháng</mat-option>
        <mat-option value="Y">Năm</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div *ngIf="this.dataSource.length > 1" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex='grow'>
      <mat-label style="margin-bottom: 100px">Chọn tập tin</mat-label>
      <input type="text" hidden matInput formControlName="files">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8" style="width: 100%;margin-top: 20px">
        <ng-container matColumnDef="stt">
          <th mat-header-cell *matHeaderCellDef>STT</th>
          <td mat-cell *matCellDef="let element; let i = index">
            {{i+1}}
          </td>
        </ng-container>
        <ng-container matColumnDef="filename">
          <th mat-header-cell *matHeaderCellDef>Tập tin</th>
          <td mat-cell *matCellDef="let element">
            {{element?.filename}}
          </td>
        </ng-container>
        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef>Chọn</th>
          <td mat-cell *matCellDef="let element">
            <mat-checkbox (change)="pushOrPop(element?.id)" [checked]="this.selectedFileIds.includes(element?.id)">
            </mat-checkbox>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </mat-form-field>
  </div>
  <div *ngIf="infoWarningMsg" class="row-warning">{{infoWarningMsg}}</div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='17' class="addBtn"
            (click)="onSave()"
            [disabled]="saveForm.invalid || this.selectedFileIds.length===0">
      <span i18n>Lưu lại</span>
    </button>
  </div>
</form>




