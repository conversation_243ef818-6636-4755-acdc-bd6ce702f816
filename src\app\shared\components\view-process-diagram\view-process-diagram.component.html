<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title> {{processName}} </h3>
<div class="dialog_content" id="print-section">
  <app-process-diagram [url]="diagramUrl" class="diagram-container"></app-process-diagram>
</div>
<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center" >
  <button mat-flat-button class="applyBtn" (click)="processConfig()" *ngIf="bpmProcessDefinitionId != '' && bpmProcessDefinitionId != null && bpmProcessDefinitionId != undefined && (checkOneGateAdminMaster === 1 || checkOneGateProcessCatalog === 1)">
      <mat-icon>settings_suggest</mat-icon>
      <span ><PERSON><PERSON>u hình quy trình</span>
  </button>
  <button mat-flat-button class="applyBtn" (click)="print()">
      <mat-icon>print</mat-icon>
      <span > In trang</span>
  </button>
</div>
