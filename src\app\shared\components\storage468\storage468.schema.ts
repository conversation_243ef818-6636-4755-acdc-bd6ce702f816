export interface IForm {
  _id?: string;
  title?: string;
}

export interface IFormOrgin {
  id?: string;
  code?: number;
  name?: string;
  form?: IForm
}

export interface IFormOrginInFoInput {
  formOrginId?: string;
  identityDoc?: string;
  identityNumber?: string;
  fullname?: string;
  birthday?: string;
  fileId?:string;
  files?:IFileInfo[];
  isIdentity:boolean;
  expire?:boolean;
  oldIdentityDoc?:string;
}

export interface IFormOrginSave {
  formOrginId?: string;
  identityDoc?: string;
  identityNumber?: string;
  fullname?: string;
  birthday?: string;
  files?:IFileInfo[];
  expiryDate?: string;
  applyDate?: string;
  aboutTime?: string;
  createUser?: userInfo;
  agency?: agencyInfo
}

export interface IFormOrginInFoOutput {
  id?: string;
  identityDoc?: string;
  createDate?: string;
  files?: IFileInfo[],
  info?: any,
  formOrgin: IFormOrgin
}

export interface IFileInfo {
  id?: string;
  filename?: string;
  extension?: string;
  mimeType?: string;
  size?:number
}

export interface userInfo {
  identityNumber?: string;
  fullname?: string;
  birthday?: string;
}

export interface agencyInfo {
  code?: string;
  name?: string;
}
