export const environment = {
  production: true,
  loadConfigFromUrl: true,
  configUrl: './assets/app.config.json',
  routerConfig: {
    enableTracing: false
  },
  config: {
    keycloakDebug: true,
    keycloakOptions: {
      config: {
        url: 'https://oidctest.vncitizens.vn/auth',
        realm: 'digo',
        clientId: 'web-padsvc'
      },
      initOptions: {
        onLoad: 'check-sso',
        checkLoginIframe: false
      },
      bearerExcludedUrls: ['/sy/app-deployment'],
      loadUserProfileAtStartUp: true
    },
    insufficientPermissionRouterLink: 'error/insufficient-permission',
    apiProviders: {
      digo: {
        rootUrl: 'https://apitest.vncitizens.vn',
        services: {
          basedata: { path: 'ba' },
          basecat: { path: 'bt' },
          fileman: { path: 'fi' },
          human: { path: 'hu' },
          postman: { path: 'po' },
          logman: { path: 'lo' },
          surfeed: { path: 'su' },
          messenger: { path: 'me' },
          padman: { path: 'pa' },
          basepad: { path: 'bd' },
          reporter: { path: 're' },
          bpm: { path: 'bpm' },
          modeling: { path: 'modeling-service' },
          adapter: { path: 'integration' },
          chatbotadm: { path: 'botadm'}
        }
      }
    },
    defaultFormIO: {
      eForm: '5f86700730ea36001c3825f9',
      applicantEForm: '5fdc64e6576b78001d53219d'
    },
    onlinePaymentMethodId: '5f7fca9fb80e603d5300dcf5',
    ancestorPlaceId: '5def47c5f47614018c000082',
    tagProvince: '5f9a73e02994dc687e68b593',
    tagDistrict: '5fec06bd1b53d8779bc9ea8b',
    tagWards: '5fec06e61b53d8779bc9ea8c',
    registerURL: 'https://accountdev.vncitizens.vn/vi/my-account/sign-up',
    cloudStaticURL: 'https://staticv2.vnptigate.vn/',
    webAccountURL: 'https://accountdev.vncitizens.vn',
    birtviewerURL: 'https://birtviewerdev.vncitizens.vn/',
    formioURL: 'https://apitest.vncitizens.vn/eform/',
    supportFile: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
    deploymentId: '5ee091507d567c9fe29f82fa',
    deploymentIdCloud: '5ee091507d567c9fe29f82fa',
    deploymentUrl: 'https://apicloud.vncitizens.vn/sy',
    appCode: 'web-padsvc',
    subsystemId: '5f7acbea0c64501bbc0706be',
    subsystemWebPadsvcId: '5f7c16069abb62f511880003',
    tutorialLink: 'https://www.youtube.com/embed/3bIGgf224ck?controls=0',
    guideTagId: '598f1f77bcf86cd799439012',
    agencyLevelCategoryId: '5f3a491c4e1bd312a6f00009',
    applyOnlineStepCategoryId: '5f3a491c4e1bd312a6f00029',
    procedureLevelCategoryId: '5f3a491c4e1bd312a6f00008',
    documentTypeCategoryId: '5f3a491c4e1bd312a6f00002',
    implementerCategoryId: '5f5b27fd4e1bd312a6f3ae1f',
    citizenId: '5f3f3c044e1bd312a6f3addf',
    enterpriseId: '5f3f3c2b4e1bd312a6f3ade0',
    districtTagId: '5f8e40db8fffa53e4c073def',
    villageTagId: '5f6b177a4e1bd312a6f3ae4a',
    departmentTagId: '5fc64a9f86940eab2fb01092',
    receivingBallotTagId: '5f7fc83eb80e603d5300dce7',
    procedureLevel2Id: '5f5b2c2b4e1bd312a6f3ae23',
    businessRegistrationConfigId: '5fa4bdb432c7c6aed8a795bf',
    dossierReceivingKindCategoryId: '5f3a491c4e1bd312a6f00032',
    receiveResultsByAddress: '5f8969018fffa53e4c073dee',
    placeProvinceTypeId: '5ee304423167922ac55bea01',
    placeDistrictTypeId: '5ee304423167922ac55bea03',
    placeWardTypeId: '5ee304423167922ac55bea03',
    vnpostConfigId: '5f9a335e32c7c6aed8630235',
    vnpostVNPostFeeId: '5f8969018fffa53e4c073dee',
    applicantTypeId: '5f7fc6cfb80e603d5300dce4',
    wardsDefaultId: '5def47c5f47614018c118034',
    paymentMethodCategoryId: '5f3a491c4e1bd312a6f00011',
    webPadsvcSubsystemId: '5f7c16069abb62f511880006',
    agencyMinistryLevelId: '5ff6b1a706d0e31c6bf13e09',
    urlWebChatBot: 'http://localhost:4200/chat',
    //chatbot
    titleChatBot: 'TỔNG ĐÀI 1022',
    logoAgency: 'assets/img/phone.svg',
    defaultColor: '#ce7a58',
    defaultTitleColor: 'white',
    logoBot: 'assets/img/custumer_sv.svg',
    defaultUserAvatarChatBot: 'assets/img/account_default.svg',
    urlChatBotRasa: 'https://digo-api.vnptigate.vn/bot/webhooks/igate/webhook',
    chatbotRestartMinute: 1,
    domainFileChatbot: 'https://padsvctest.vncitizens.vn/',
    //
    chatbotConfigure: '1',
    configuratingChatbotId: '606e6764b6c5d41d6b6d30e7',
    languageDefaultId: 228,
    languageDefault: 'vi',
    reloadTimeout: 2000,
    expiredTime: 3000,
    translatePaginator: ['Số dòng', 'Trang đầu', 'Trang trước', 'Trang tiếp theo', 'Trang cuối', 'của'],
    pageSizeOptions: [5, 10, 20, 50],
    searchAfterStopTyping: 800,
    fileUnits: ['bytes', 'KB', 'MB', 'GB', 'TB', 'PB'],
    regValidators: '/[-a-zA-Z0-9@:%_\+.~#?&//=]{2,256}\.[a-z]{2,4}\b(\/[-a-zA-Z0-9@:%_\+.~#?&//=]*)?/gi',
    procedureFormAcceptFileExtension: ['.DOC', '.DOCX', '.PDF', '.XLS', '.XLSX', '.TXT', '.JPG', '.JPEG', '.PNG'],
    procedureFormAcceptFileType: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png'
    ],
    procedureFormMaxFileSize: '5242880',
    rootAgency: {
      id: '5f39fa505224cf235e134c69',
      code: '000.00.00.H58',
      trans: {
        vi: {
          name: 'UBND Tỉnh Tiền Giang'
        },
        en: {
          name: 'People\'s Committee of Tien Giang province'
        }
      },
      provinceId: 82,
    },
    defaultUserAvatar: 'https://staticv2.vnptigate.vn/logo/HCC.png',
    defaultLogo: 'https://staticv2.vnptigate.vn/logo/quoc-huy.png',
    defaultBanner: 'https://staticv2.vnptigate.vn/img/du-lich-mien-tay.jpg',
    getPublicTokenURL: 'https://digo-oidc.vnptigate.vn/auth/realms/digo/protocol/openid-connect/token',
    GRANT_TYPE: 'client_credentials',
    CLIENT_ID: 'web-padsvc-public',
    SCOPE: 'openid',
    CLIENT_SECRET: '1bbc4b3d-0274-4d40-9398-0be23c8793fe',
    siteTitle: {
      vi: 'Chính quyền số',
      en: 'Digital Government'
    },
    dossierEmailSMSZaloConfig: {
      sms: {
        enable: 1,
        message: {
          vi: 'Nộp hồ sơ thành công! Mã số hồ sơ: {{code}}',
          en: 'Submit successfully! Dossier code: {{code}}'
        },
        characterLimit: 150
      }
    },
    searchMappingDataType: {
      district: '5fc0707a62681a8bef000005',
      province: '5fc0707a62681a8bef000004',
      agency: '5fc0707a62681a8bef000007',
      ward: '5fc0707a62681a8bef000006'
    },
    uiLoaderConfig: {
      bgsColor: '#000000',
      bgsOpacity: 0.5,
      bgsPosition: 'bottom-right',
      bgsSize: 60,
      bgsType: 'ball-spin-clockwise',
      blur: 0,
      delay: 0,
      fastFadeOut: true,
      fgsColor: '#eb6a35',
      fgsPosition: 'center-center',
      fgsSize: 120,
      fgsType: 'ball-scale-multiple',
      gap: 24,
      masterLoaderId: 'master',
      overlayBorderRadius: '0',
      overlayColor: 'rgb(255,255,255)',
      pbColor: 'red',
      pbDirection: 'ltr',
      pbThickness: 3,
      hasProgressBar: false,
      text: '',
      textColor: '#FFFFFF',
      textPosition: 'center-center',
      maxTime: -1,
      minTime: 300
    },
    dossierTaskStatus: {
      justRegistered: {
        id: '60e409823dfc9609723e493c',
        vi: 'Mới đăng ký',
        en: 'Just registered'
      },
      waitingAdditional: {
        id: '60e409983dfc9609723e493e',
        vi: 'Chờ bổ sung',
        en: 'Waiting additional'
      },
      requestForAdditionalDocuments: {
        id: '60ebf03109cbf91d41f87f8b',
        vi: 'Yêu cầu bổ sung giấy tờ',
        en: 'Request for additional documents'
      },
      dossierAdded: {
        id: '60ebf0db09cbf91d41f87f8c',
        vi: 'Đã bổ sung hồ sơ',
        en: 'Dossier added'
      },
      notAccepted: {
        id: '60ebf14b09cbf91d41f87f8d',
        vi: 'Không được tiếp nhận',
        en: 'Not accepted'
      },
      resultReturned: {
        id: '60ebf17309cbf91d41f87f8e',
        vi: 'Đã trả kết quả',
        en: 'Result returned'
      },
      cancelled: {
        id: '60ed1a5909cbf91d41f87f9f',
        vi: 'Hồ sơ đã hủy',
        en: 'Cancelled'
      },
      pending: {
        id: '60ed1b7c09cbf91d41f87fa0',
        vi: 'Đang tạm dừng',
        en: 'Pending'
      },
      hadResult: {
        id: '60ed1c3409cbf91d41f87fa1',
        vi: 'Có kết quả',
        en: 'Had result'
      }
    },
    dossierMenuTaskRemind: {
      justRegistered: {
        id: '60f52e0d09cbf91d41f88834',
        vi: 'Mới đăng ký',
        en: 'Just registered'
      }
    },
    publicFileUrl: "https://apitest.vnptigate.vn/upload/images",
    igateSubsystemId: '5f7c16069abb62f511880003',
    signPositionDigitalSignature: '10,10,150,70',
    locationDigitalSignature: 'Ký tên',
    messageDisplayDigitalSignature: 'Vui long nhap PIN ky so',
  }
};
