import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {FormOrginService} from "data/service/form-orgin/form-orgin.service";
import {IFormOrginInFoInput, IFormOrginInFoOutput} from "shared/components/storage468/storage468.schema";

@Component({
  selector: 'choose-form-orgin',
  templateUrl: './choose-form-orgin.component.html',
  styleUrls: ['./choose-form-orgin.component.scss']
})
export class ChooseFormOrginComponent{
  displayedColumns: string[] = ['code', 'createDate', 'files', 'action'];
  constructor(public dialogRef: MatDialogRef<ChooseFormOrginComponent>,
              @Inject(MAT_DIALOG_DATA) public data: {
                formOrginInfos?:IFormOrginInFoOutput[],
                title: string}) { }

  onDismiss() {
    this.dialogRef.close();
  }
}
