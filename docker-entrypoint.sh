#!/bin/sh

echo "----------------"
echo "ENTRYPOINT START"
echo "----------------"
echo ""

digo_supported_lang="vi en"
for lang in $digo_supported_lang; do

  index_file="./${lang}/index.html"
  index_bak_file="./${lang}/index.html.bak"
  config_file="./${lang}/assets/app.config.json"
  config_bak_file="./${lang}/assets/app.config.json.bak"
  exists_bak_file=1

  if [ ! -f $config_bak_file ]; then
    echo "> copy $config_file to $config_bak_file";
    cp $config_file $config_bak_file
    echo "> copy $index_file to $index_bak_file";
    cp $index_file $index_bak_file
    exists_bak_file=0
  fi

  if [ $exists_bak_file -eq 1 ]; then
    echo "> copy $config_bak_file to $config_file";
    cp $config_bak_file $config_file;
    echo "> copy $index_bak_file to $index_file";
    cp $index_bak_file $index_file;
  fi

  if [ -n "${KEYCLOAK_URL}" ]; then
    replace='\/'
    encodedValue=${KEYCLOAK_URL//\//$replace}
    sed -e "s/\"url\": \"\${KEYCLOAK_URL}\"/\"url\": \"$encodedValue\"/g" -i $config_file
  fi

  if [ -n "${KEYCLOAK_REALM}" ]; then
    sed -e "s/\"realm\": \"\${KEYCLOAK_REALM}\"/\"realm\": \"${KEYCLOAK_REALM}\"/g" -i $config_file
  fi

  if [ -n "${KEYCLOAK_CLIENT_ID}" ]; then
    sed -e "s/\"clientId\": \"\${KEYCLOAK_CLIENT_ID}\"/\"clientId\": \"${KEYCLOAK_CLIENT_ID}\"/g" -i $config_file
  fi

  if [ -n "${DIGO_API_GATEWAY}" ]; then
    replace='\/'
    encodedValue=${DIGO_API_GATEWAY//\//$replace}
    sed -e "s/\"rootUrl\": \"\${DIGO_API_GATEWAY}\"/\"rootUrl\": \"$encodedValue\"/g" -i $config_file
  fi

  if [ -n "${STATIC_URL}" ]; then
    sed -e "s,\${STATIC_URL},${STATIC_URL},g" -i $config_file
  fi

  if [ -n "${REGISTER_URL}" ]; then
    sed -e "s,\${REGISTER_URL},${REGISTER_URL},g" -i $config_file
  fi

  if [ -n "${DEPLOYMENT_ID}" ]; then
    sed -e "s,\${DEPLOYMENT_ID},${DEPLOYMENT_ID},g" -i $config_file
  fi

  if [ -n "${TUTORIAL_LINK}" ]; then
    sed -e "s,\${TUTORIAL_LINK},${TUTORIAL_LINK},g" -i $config_file
  fi

  if [ -n "${ROOT_AGENCY_ID}" ]; then
    sed -e "s,\${ROOT_AGENCY_ID},${ROOT_AGENCY_ID},g" -i $config_file
  fi

  if [ -n "${ROOT_AGENCY_CODE}" ]; then
    sed -e "s,\${ROOT_AGENCY_CODE},${ROOT_AGENCY_CODE},g" -i $config_file
  fi

  if [ -n "${ROOT_AGENCY_NAME_VI}" ]; then
    sed -e "s,\${ROOT_AGENCY_NAME_VI},${ROOT_AGENCY_NAME_VI},g" -i $config_file
  fi

  if [ -n "${ROOT_AGENCY_NAME_EN}" ]; then
    sed -e "s,\${ROOT_AGENCY_NAME_EN},${ROOT_AGENCY_NAME_EN},g" -i $config_file
  fi

  if [ -n "${ROOT_PROVINCE_ID}" ]; then
    sed -e "s,\${ROOT_PROVINCE_ID},${ROOT_PROVINCE_ID},g" -i $config_file
  fi

  if [ -n "${DEFAULT_USER_AVATAR}" ]; then
    sed -e "s,\${DEFAULT_USER_AVATAR},${DEFAULT_USER_AVATAR},g" -i $config_file
  fi

  if [ -n "${DEFAULT_LOGO}" ]; then
    sed -e "s,\${DEFAULT_LOGO},${DEFAULT_LOGO},g" -i $config_file
  fi

  if [ -n "${DEFAULT_BANNER}" ]; then
    sed -e "s,\${DEFAULT_BANNER},${DEFAULT_BANNER},g" -i $config_file
  fi

  if [ -n "${PUBLIC_TOKEN_URL}" ]; then
    sed -e "s,\${PUBLIC_TOKEN_URL},${PUBLIC_TOKEN_URL},g" -i $config_file
  fi

  if [ -n "${PUBLIC_GRANT_TYPE}" ]; then
    sed -e "s,\${PUBLIC_GRANT_TYPE},${PUBLIC_GRANT_TYPE},g" -i $config_file
  fi

  if [ -n "${VI_SITETITLE}" ]; then
    sed -e "s,\${VI_SITETITLE},${VI_SITETITLE},g" -i $config_file
  fi

  if [ -n "${EN_SITETITLE}" ]; then
    sed -e "s,\${EN_SITETITLE},${EN_SITETITLE},g" -i $config_file
  fi

  # if [ -n "${OAUTH2_USERNAME}" ]; then
  #   sed -e "s,\${OAUTH2_USERNAME},${OAUTH2_USERNAME},g" -i $config_file
  # fi

  # if [ -n "${OAUTH2_PASSWORD}" ]; then
  #   sed -e "s,\${OAUTH2_PASSWORD},${OAUTH2_PASSWORD},g" -i $config_file
  # fi

  if [ -n "${OAUTH2_SCOPE}" ]; then
    sed -e "s,\${OAUTH2_SCOPE},${OAUTH2_SCOPE},g" -i $config_file
  fi

  if [ -n "${OAUTH2_CLIENT_ID}" ]; then
    sed -e "s,\${OAUTH2_CLIENT_ID},${OAUTH2_CLIENT_ID},g" -i $config_file
  fi

  if [ -n "${CLIENT_SECRET}" ]; then
    sed -e "s,\${CLIENT_SECRET},${CLIENT_SECRET},g" -i $config_file
  fi

  if [ -n "${WEB_ACCOUNT_URL}" ]; then
    sed -e "s,\${WEB_ACCOUNT_URL},${WEB_ACCOUNT_URL},g" -i $config_file
  fi

  if [ -n "${BIRT_VIEWER_URL}" ]; then
    sed -e "s,\${BIRT_VIEWER_URL},${BIRT_VIEWER_URL},g" -i $config_file
  fi

  if [ -n "${RECEIVING_BALLOT_TAG_ID}" ]; then
    sed -e "s,\${RECEIVING_BALLOT_TAG_ID},${RECEIVING_BALLOT_TAG_ID},g" -i $config_file
  fi

  if [ -n "${GUIDE_TAG_ID}" ]; then
    sed -e "s,\${GUIDE_TAG_ID},${GUIDE_TAG_ID},g" -i $config_file
  fi

  if [ -n "${APPLY_ONLINE_STEP_CATEGORY_ID}" ]; then
    sed -e "s,\${APPLY_ONLINE_STEP_CATEGORY_ID},${APPLY_ONLINE_STEP_CATEGORY_ID},g" -i $config_file
  fi

  if [ -n "${PROCEDURE_LEVEL_CATEGORY_ID}" ]; then
    sed -e "s,\${PROCEDURE_LEVEL_CATEGORY_ID},${PROCEDURE_LEVEL_CATEGORY_ID},g" -i $config_file
  fi

  if [ -n "${DOCUMENT_TYPE_CATEGORY_ID}" ]; then
    sed -e "s,\${DOCUMENT_TYPE_CATEGORY_ID},${DOCUMENT_TYPE_CATEGORY_ID},g" -i $config_file
  fi

  if [ -n "${IMPLEMENTER_CATEGORY_ID}" ]; then
    sed -e "s,\${IMPLEMENTER_CATEGORY_ID},${IMPLEMENTER_CATEGORY_ID},g" -i $config_file
  fi

  if [ -n "${CITIZEN_ID}" ]; then
    sed -e "s,\${CITIZEN_ID},${CITIZEN_ID},g" -i $config_file
  fi

  if [ -n "${ENTERPRISE_ID}" ]; then
    sed -e "s,\${ENTERPRISE_ID},${ENTERPRISE_ID},g" -i $config_file
  fi

  if [ -n "${DISTRICT_TAG_ID}" ]; then
    sed -e "s,\${DISTRICT_TAG_ID},${DISTRICT_TAG_ID},g" -i $config_file
  fi

  if [ -n "${VILLAGE_TAG_ID}" ]; then
    sed -e "s,\${VILLAGE_TAG_ID},${VILLAGE_TAG_ID},g" -i $config_file
  fi

  if [ -n "${DEPARTMENT_TAG_ID}" ]; then
    sed -e "s,\${DEPARTMENT_TAG_ID},${DEPARTMENT_TAG_ID},g" -i $config_file
  fi

  if [ -n "${PROCEDURE_LEVEL_1_ID}" ]; then
    sed -e "s,\${PROCEDURE_LEVEL_1_ID},${PROCEDURE_LEVEL_1_ID},g" -i $config_file
  fi

  if [ -n "${PROCEDURE_LEVEL_2_ID}" ]; then
    sed -e "s,\${PROCEDURE_LEVEL_2_ID},${PROCEDURE_LEVEL_2_ID},g" -i $config_file
  fi

  if [ -n "${BUSINESS_REGISTRATION_CONFIG_ID}" ]; then
    sed -e "s,\${BUSINESS_REGISTRATION_CONFIG_ID},${BUSINESS_REGISTRATION_CONFIG_ID},g" -i $config_file
  fi

  if [ -n "${ANCESTOR_PLACE_ID}" ]; then
    sed -e "s,\${ANCESTOR_PLACE_ID},${ANCESTOR_PLACE_ID},g" -i $config_file
  fi

  if [ -n "${TAG_PROVINCE}" ]; then
    sed -e "s,\${TAG_PROVINCE},${TAG_PROVINCE},g" -i $config_file
  fi

  if [ -n "${TAG_DISTRICT}" ]; then
    sed -e "s,\${TAG_DISTRICT},${TAG_DISTRICT},g" -i $config_file
  fi

  if [ -n "${TAG_WARDS}" ]; then
    sed -e "s,\${TAG_WARDS},${TAG_WARDS},g" -i $config_file
  fi

  if [ -n "${SMS_APPLY_SUCCESSFULLY_VI}" ]; then
    sed -e "s,\${SMS_APPLY_SUCCESSFULLY_VI},${SMS_APPLY_SUCCESSFULLY_VI},g" -i $config_file
  fi

  if [ -n "${SMS_APPLY_SUCCESSFULLY_EN}" ]; then
    sed -e "s,\${SMS_APPLY_SUCCESSFULLY_EN},${SMS_APPLY_SUCCESSFULLY_EN},g" -i $config_file
  fi

  if [ -n "${SUBSYSTEM_ID}" ]; then
    sed -e "s,\${SUBSYSTEM_ID},${SUBSYSTEM_ID},g" -i $config_file
  fi

  if [ -n "${MAPPING_DATATYPE_DISTRICT}" ]; then
    sed -e "s,\${MAPPING_DATATYPE_DISTRICT},${MAPPING_DATATYPE_DISTRICT},g" -i $config_file
  fi

  if [ -n "${MAPPING_DATATYPE_PROVINCE}" ]; then
    sed -e "s,\${MAPPING_DATATYPE_PROVINCE},${MAPPING_DATATYPE_PROVINCE},g" -i $config_file
  fi

  if [ -n "${MAPPING_DATATYPE_AGENCY}" ]; then
    sed -e "s,\${MAPPING_DATATYPE_AGENCY},${MAPPING_DATATYPE_AGENCY},g" -i $config_file
  fi

  if [ -n "${MAPPING_DATATYPE_WARD}" ]; then
    sed -e "s,\${MAPPING_DATATYPE_WARD},${MAPPING_DATATYPE_WARD},g" -i $config_file
  fi

  if [ -n "${PROCEDURE_FORM_MAX_FILESIZE}" ]; then
    sed -e "s,\${PROCEDURE_FORM_MAX_FILESIZE},${PROCEDURE_FORM_MAX_FILESIZE},g" -i $config_file
  fi

  if [ -n "${URL_WEB_CHATBOT}" ]; then
    sed -e "s,\${URL_WEB_CHATBOT},${URL_WEB_CHATBOT},g" -i $config_file
  fi

  if [ -n "${URL_CHATBOT_RASA}" ]; then
    sed -e "s,\${URL_CHATBOT_RASA},${URL_CHATBOT_RASA},g" -i $config_file
    echo "${URL_CHATBOT_RASA}"
  fi

  if [ -n "${CHATBOT_CONFIGURE}" ]; then
    sed -e "s,\${CHATBOT_CONFIGURE},${CHATBOT_CONFIGURE},g" -i $config_file
  fi

  if [ -n "${TITLE_CHATBOT}" ]; then
    sed -e "s,\${TITLE_CHATBOT},${TITLE_CHATBOT},g" -i $config_file
  fi

  if [ -n "${DIGO_CONFIGURATING_CHATBOT_ID}" ]; then
    sed -e "s,\${DIGO_CONFIGURATING_CHATBOT_ID},${DIGO_CONFIGURATING_CHATBOT_ID},g" -i $config_file
  fi

  if [ -n "${CHATBOT_RESTART_MINUTE}" ]; then
    sed -e "s,\${CHATBOT_RESTART_MINUTE},${CHATBOT_RESTART_MINUTE},g" -i $config_file
  fi

  if [ -n "${DOMAIN_FILE_CHATBOT}" ]; then
    sed -e "s,\${DOMAIN_FILE_CHATBOT},${DOMAIN_FILE_CHATBOT},g" -i $config_file
  fi

  if [ -n "${FORM_IO_URL}" ]; then
    sed -e "s,\${FORM_IO_URL},${FORM_IO_URL},g" -i $config_file
  fi

  if [ -n "${DEPLOYMENT_ID_ClOUD}" ]; then
    sed -e "s,\${DEPLOYMENT_ID_ClOUD},${DEPLOYMENT_ID_ClOUD},g" -i $config_file
  fi

  if [ -n "${DIGO_DEPLOYMENT_URL}" ]; then
    sed -e "s,\${DIGO_DEPLOYMENT_URL},${DIGO_DEPLOYMENT_URL},g" -i $config_file
  fi

  if [ -n "${APP_CODE}" ]; then
    sed -e "s,\${APP_CODE},${APP_CODE},g" -i $config_file
  fi

  if [ -n "${DEFAULT_FORM_IO}" ]; then
    sed -e "s/\"defaultFormIO\": \"\${DEFAULT_FORM_IO}\"/\"defaultFormIO\": ${DEFAULT_FORM_IO}/g" -i $config_file
    echo "${DEFAULT_FORM_IO}"
  fi

  if [ -n "${REPORTER_LOCATION}" ]; then
      sed -e "s/\"reporterLocation\": \"\${REPORTER_LOCATION}\"/\"reporterLocation\": ${REPORTER_LOCATION}/g" -i $config_file
      echo "${REPORTER_LOCATION}"
  fi

  if [ -n "${PLACE_PROVINCE_TYPE_ID}" ]; then
    sed -e "s,\${PLACE_PROVINCE_TYPE_ID},${PLACE_PROVINCE_TYPE_ID},g" -i $config_file
  fi

  if [ -n "${PLACE_DISTRICT_TYPE_ID}" ]; then
    sed -e "s,\${PLACE_DISTRICT_TYPE_ID},${PLACE_DISTRICT_TYPE_ID},g" -i $config_file
  fi

  if [ -n "${PLACE_WARD_TYPE_ID}" ]; then
    sed -e "s,\${PLACE_WARD_TYPE_ID},${PLACE_WARD_TYPE_ID},g" -i $config_file
  fi

  if [ -n "${NATION_ID}" ]; then
    sed -e "s,\${NATION_ID},${NATION_ID},g" -i $config_file
  fi

  if [ -n "${SUBSYSTEM_ID_PADSVC}" ]; then
    sed -e "s,\${SUBSYSTEM_ID_PADSVC},${SUBSYSTEM_ID_PADSVC},g" -i $config_file
  fi

  if [ -n "${QNA_URL}" ]; then
    sed -e "s,\${QNA_URL},${QNA_URL},g" -i $config_file
  fi

  if [ -n "${PUBLIC_FILE_URL}" ]; then
    sed -e "s,\${PUBLIC_FILE_URL},${PUBLIC_FILE_URL},g" -i $config_file
  fi

  if [ -n "${DIGO_CHATBOT_EXCLUDE_URLS}" ]; then
    excludeUrls=${DIGO_CHATBOT_EXCLUDE_URLS}
  else
    excludeUrls="[\"thong-ke-mobile\"]"
  fi
  sed -e "s/\"chatbotExcludeUrls\": \"\${DIGO_CHATBOT_EXCLUDE_URLS}\"/\"chatbotExcludeUrls\": $excludeUrls/g" -i $config_file


done

nginx -g "daemon off;"
 m