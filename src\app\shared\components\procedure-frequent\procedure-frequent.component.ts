import {Component, Input, OnInit} from '@angular/core';
import { ReporterService } from 'src/app/data/service/svc-reporter/reporter.service';
import { BasepadService } from 'src/app/data/service/svc-basepad/basepad.service';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { DeploymentService } from 'src/app/data/service/deployment.service';


@Component({
  selector: 'app-procedure-frequent',
  templateUrl: './procedure-frequent.component.html',
  styleUrls: ['./procedure-frequent.component.scss']
})
export class ProcedureFrequentComponent implements OnInit {
  @Input() useCustomStyleHPG: boolean = false;
  listProcedureFrequent = [];
  ShowTheNewInterfaceDVCTP = this.deploymentService?.getAppDeployment()?.ShowTheNewInterfaceDVCTP;
  enableShowAgencyFrequent = this.deploymentService?.getAppDeployment()?.enableShowAgencyFrequent == true ? true : false;

  constructor(
    private reporterService: ReporterService,
    private basepadService: BasepadService,
    private router: Router,
    private deploymentService: DeploymentService
  ) {}

  ngOnInit(): void {
    this.getListProcedureFrequent();
  }

  getListProcedureFrequent() {
    this.listProcedureFrequent = [];
    this.reporterService.getListProcedureFrequentV2().subscribe(data => {
      data.forEach(pro => {
        if (this.enableShowAgencyFrequent){
          this.basepadService.getProcedureDetail(pro.id).subscribe(res => {
            pro.agencyName = res.agencyIssued;
          });
        }
        this.listProcedureFrequent.push(pro);
      });
    });
  }

  changeProcedure(id) {
    this.router.navigate(['procedure/detail/' + id], {});
    const element = document.querySelector('mat-sidenav-content') || window;
    element.scrollTo(0, 0);
  }
}
