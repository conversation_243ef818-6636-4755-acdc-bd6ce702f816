import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ProcessHandleComponent } from './process-handle.component';

describe('ProcessHandleComponent', () => {
  let component: ProcessHandleComponent;
  let fixture: ComponentFixture<ProcessHandleComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ProcessHandleComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcessHandleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
