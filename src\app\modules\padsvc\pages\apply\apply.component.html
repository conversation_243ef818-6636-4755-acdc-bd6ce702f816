<ngx-ui-loader></ngx-ui-loader>
<div fxLayout="row" fxLayoutAlign="center">
    <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'breadcrumb-custom' : 'breadcrumb'" fxFlex.gt-sm="88" fxFlex="95">
        <a [routerLink]="offWebPadsvc==1 ? null : '/'" i18n>Trang chủ</a>
        <mat-icon>navigate_next</mat-icon>
        <a [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''"  [routerLink]="offWebPadsvc==1 ? null :ShowTheNewInterfaceDVCTP === 1 ? '/procedure-online/search' : '/padsvc'" i18n>Dịch vụ công trực tuyến</a>
    </div>
</div>

<div *ngIf="isWaiting && showWaiting" class="waiting-container">
    <div class="spinner-wrapper">
        <mat-spinner color="warn"></mat-spinner>
        <span class="spinner-text"><PERSON><PERSON> chuyể<PERSON> sang màn hình thanh toán. Vui lòng chờ trong giây lát!</span>
    </div>
</div>

<div *ngIf="isCheckingInfoEnterprise" class="waiting-container opacity-div">
    <div class="spinner-wrapper">
        <mat-spinner color="warn"></mat-spinner>
        <span class="spinner-text">Đang kiểm tra thông tin. Vui lòng chờ trong giây lát</span>
    </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <div class="aoContent" fxFlex.gt-sm="88" fxFlex="95">
        <h2 class="title"><span *ngIf="showProcedureCode == true">{{ procedureDetail[0]?.code }}: </span>{{ procedureDetail[0]?.name }} <mat-icon *ngIf="selectedShowEdit" (click)="openAgency()" class="material-symbols-outlined" style="font-size: 14px;">
            edit
            </mat-icon></h2>
        <h3 [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'agencyNew' : 'agency'" *ngIf="agencyInfo[0]?.name !== undefined && agencyInfo[0]?.name.length > 0">
            <span *ngFor="let agency1 of agencyInfo[0].name">
                <span *ngIf="agency1.languageId == selectedLangId"  [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'agencyNameNew' : 'agencyName'" >
                    <mat-icon>account_balance</mat-icon>
                    <span>{{agency1.name}}</span>
                </span>
            </span>
        </h3>
        <h3 class="level">
            <mat-icon>label_important</mat-icon>
            <span>{{ procedureDetail[0]?.level?.name }}</span>
        </h3>
        <h3>
            <span><span style="font-weight: 500;">Quy trình:</span> {{ selectedProcessFromUrl?.processDefinition?.name }}</span>
        </h3>
        <h3>
            <p *ngIf="qbhAddInfoToFiles"class="sub_title"><span class="bold">Thông tin liên hệ hỗ trợ:</span>&nbsp;<span  class="bold" style="color: red;">{{this.hotline}}</span></p>
        </h3>
        <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'stepper stepperNew' : 'stepper'">
            <mat-horizontal-stepper labelPosition="bottom" #stepper>
                <mat-step   [label]="labelStep1">
                    <form [formGroup]="verifyForm" (change)="onChangeForm($event)">
                        <div class="ctrl" *ngIf="isScanCode" fxLayoutAlign="end center">
                          <app-button-scanner-barcode
                            fxFlex.gt-sm="12" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex="grow"
                            (barCode)="setInfoKgg($event)"
                            [classButton]="'listen-scanner-button'"
                            [size]="25"></app-button-scanner-barcode>
                        </div>
                      </form>

                    <div class="s_head">
                        <span>Thông tin người nộp</span>
                                                <div fxFlex='1'></div>
                        <button type="button" mat-stroked-button  class="btnSecondary" (click)="getListInformationDossierOld()" *ngIf="isShowCopyDossierAppliedButtonApplicant">
                            <mat-icon>save</mat-icon>
                            <span>Sao chép thông tin từ hồ sơ cũ</span>
                          </button>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div class="thoaiisolate ">
                        <formio #applicantEFormComp
                                [form]="applicantEForm.component"
                                [submission]="applicantEForm.data"
                                [renderOptions]="applicantEForm.renderOptions"
                                [readOnly]="isLoading"
                                [viewOnly]="isLoading"
                                (change)="onChangeForm($event)"
                                (customEvent)="eventAutofillEntireEfromEnable($event)"
                                *ngIf="applicantEForm.id != undefined && applicantEForm.id != ''">
                        </formio>
                    </div>
                    <br>
                    
                    <div  *ngIf="isAutoFillBusinessRegistrationEnterpriseEform"  class="button-search-dkkd" (click)="checkInforEnterprise()">
                        <span>Kiểm tra thông tin doanh nghiệp</span>
                    </div> 
                    <br><br>
                    <div *ngIf="isNotFoundInfoEnterprise">
                        <p class="text-left">
                            <span class="no-data-text">**Không tìm thấy Mã số thuế, mời bạn kiểm tra lại</span>
                        </p>
                    </div>
                    <div style="padding-bottom: 20px;">
                        <table class="is-table" *ngIf="!!originTemplate">
                            <caption>Thông tin dưới đây là thông tin nhập thêm để hỗ trợ tự điền dữ liệu ở mục thông tin chi tiết, không bắt buộc nhập</caption>
                            <thead>
                                <tr>
                                    <th>Tên trường</th>
                                    <th class="center">
                                        <span>Nhập</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <ng-container *ngFor="let item of originTemplate?.pos">
                                    <tr>
                                        <td matTooltip="{{item?.key}}">{{item?.name}}</td>
                                        <td class="center">
                                            <mat-form-field style="height: unset !important; margin-bottom: -28px;" appearance="outline"
                                                fxFlex='grow'>
                                                <mat-label>Căn cước công dân</mat-label>
                                                <input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')" matInput
                                                    maxlength="12" [(ngModel)]="item.identityNumber"
                                                    (input)="onChangeIdentityNumber(item, $event)">
                                            </mat-form-field>
                                        </td>
                                    </tr>
                                </ng-container>
                            </tbody>
                        </table>
                    </div>
                    <div class="s_head" *ngIf="!!eForm.id && eForm.component?.components.length > 0">
                        <span>Thông tin chi tiết</span>
                        <div fxFlex='1'></div>
                        <button type="button" mat-stroked-button  class="btnSecondary" (click)="getListDossierOld()" *ngIf="isShowCopyDossierAppliedButton">
                            <mat-icon>save</mat-icon>
                            <span>Sao chép thông tin từ hồ sơ cũ</span>
                          </button>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div class="test-class-agg" *ngIf="this.procedureId == this.thuTucTSDC">
                        <div class="col-md-4">
                            <form [formGroup]="tsdcForm" (submit)="getInfoTSDC()" class="col-md-6">
                                <mat-form-field appearance="outline" fxFlex='grow' class="maHocSinh" [ngClass]="{'disabled': true}">
                                    <mat-label>Số CCCD</mat-label>
                                    <input matInput formControlName="maHocSinh" value="{{applicantEForm?.data?.data?.identityNumber}}" [disabled]="isDisabled">
                                </mat-form-field>
                                <button type="submit" mat-stroked-button class="btnSecondary btn-tracuu" *ngIf="flagTraCuu == false">
                                    <span>Tra cứu thông tin</span>
                                </button>
                                <button type="submit" mat-stroked-button class="btnSecondary btn-tracuu-disabled" disabled *ngIf="flagTraCuu == true">
                                    <span>Tra cứu thông tin</span>
                                </button>
                            </form>
                        </div>
                            
                    </div>
                    <div class="thoaiisolate mistable">
                        <formio #eFormComp [form]="eForm.component"
                                [submission]="eForm.data"
                                [renderOptions]="eForm.renderOptions"
                                [readOnly]="isLoading"
                                [viewOnly]="isLoading"
                                (change)="onChangeForm($event)"
                                *ngIf="eForm.id != undefined && eForm.id != ''">
                        </formio>
                    </div>
                    <br>
                    <digo-check-send-notify functionType="applyOnline" template="none"></digo-check-send-notify>
                    <div *ngIf="enableShowCSDLQGVDCBDH && procedureDetail[0]?.isCheckHoseHoldInfo">
                        <app-add-household (formValueChanged)="onFormValueChange($event)"></app-add-household>
                    </div>
                    
                    <div *ngIf="enablePrintTicket && listMauPhieu.length >= 1">
                        <mat-form-field appearance="outline" *ngIf="enablePrintTicket && listMauPhieu.length >= 1">
                            <mat-label i18n>Mẫu phiếu</mat-label>
                            <mat-select formControlName="listMauPhieu" (selectionChange)="phieuChanged($event)"
                                        required>
                              <mat-option *ngFor="let kind of listMauPhieu" value="{{kind.id}}">
                                {{kind.name}}
                                <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                              </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="ctrl">
                        <button mat-stroked-button (click)="handleBack()" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" i18n>Quay lại</button>
                        <button type="button" mat-stroked-button *ngIf="enablePrintTicket && !hideDossierProcessAndPrint" (click)="printTicket()" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" i18n='@@printTicket'>In phiếu</button>
                        <button mat-flat-button (click)="confirmApplyS1(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Tiếp tục</button>
                    </div>
                </mat-step>
                <mat-step [label]="labelStep2" *ngIf="OS_HGI==false">
                    <div class="s_head">
                        <span i18n>Thành phần hồ sơ</span>
                    </div>
                    <div *ngIf="showNoteDocumentEnable === 1" class="mt-1" style="font-style:italic">Ghi chú: <span style="color: #de1212;">{{showNoteDocumentValue}}</span></div>
                    <div *ngIf="showNoteProfileComposition && procedureForm.length != 0 && !isNoForm" class="note-profile-composition" >
                        <span i18n="@@noteProfileCompositionNote">Ghi chú:</span>&nbsp;
                        <span class="material-icons custom-icon-note">check_circle_outline</span>
                        <span class="text-note-color" i18n="@@noteProfileCompositionTitle">Giấy tờ bắt buộc đính kèm</span>&nbsp;
                        <span i18n="@@noteProfileCompositionDescribe">(Khuyến khích đính kèm file scan dạng .pdf)</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div class="success_head" *ngIf="procedureForm.length == 0 || isNoForm">
                        <p class="title" i18n>Không có thông tin giấy tờ</p>
                    </div>
                    <div class="procedureForm">
                        <get-form-orgin
                            [fullname]="applicantEForm?.data?.data?.fullname"
                            [identityNumber]="applicantEForm?.data?.data?.identityNumber"
                            [birthday]="applicantEForm?.data?.data?.birthday"
                            #getFormOrgin>
                        </get-form-orgin>
                        <form [formGroup]="prForm">
                             <!--Thêm loại procedureFormType hiển thị dạng bảng-->
                             <div *ngIf="procedureFormType && procedureFormType == 1">
                                <table class="mat-table cdk-table mat-elevation-z4 table_display_phone" style="width: 100%;">
                                    <tr *ngIf="editRepQBH === true">
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" i18n>STT</th>
                                        <th class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" i18n="@@procedureName">Tên giấy tờ</th>
                                        <!-- <th class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" i18n="@@procedureQuantity">Số lượng bản</th> -->
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" i18n="@@procedureType">Loại bản</th>
                                        <th class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" >Nhập trực tuyến eform</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" >Tải Mẫu giấy tờ</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowQuantityElectronicAuthentication">Số bản</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">Số trang</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowCopyNumberElectronicAuthentication">Số bản sao</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">Loại chứng thực</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" style="max-width: 150px;" i18n="@@procedureAttachFile">Đính kèm giấy tờ</th>
                                        <!-- <th class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="140">
                                            <span i18n="@@procedureHasFormOrgin">Lấy giấy tờ từ form nhập thông tin</span> -->
                                            <!-- <div class="hasFormOrgin" i18n="@@procedureHasFormOrginDes">Giấy CMND hoặc hộ chiếu (bản photocopy)</div> -->
                                        <!-- </th> -->
                                    </tr>
                                    <tr *ngIf="editRepQBH === false">
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" i18n>STT</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" i18n="@@procedureName">Tên giấy tờ</th>
                                        <!-- <th class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" i18n="@@procedureQuantity">Số lượng bản</th> -->
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" style="width: 20%;" i18n="@@procedureType">Loại bản</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" i18n="@@procedureTemplate">Mẫu giấy tờ</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowQuantityElectronicAuthentication">Số bản</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">Số trang</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowCopyNumberElectronicAuthentication">Số bản sao</th>
                                        <th [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="100" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">Loại chứng thực</th>
                                        <th *ngIf="!showExtensionAllowLan" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" style="max-width: 150px;" i18n="@@procedureAttachFile">Đính kèm giấy tờ</th>
                                        <th *ngIf="showExtensionAllowLan" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'tablecolor1New' : ''" class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" style="max-width: 150px;"><span  i18n="@@procedureAttachFile">Đính kèm giấy tờ</span><br/><span class="extensionAllowLan">{{ extensionAllowLan }}</span></th>
                                        <!-- <th class="mat-header-cell cdk-header-cell tableheader1 cdk-column-id mat-column-id ng-star-inserted tablecolor1" width="140">
                                            <span i18n="@@procedureHasFormOrgin">Lấy giấy tờ từ form nhập thông tin</span> -->
                                            <!-- <div class="hasFormOrgin" i18n="@@procedureHasFormOrginDes">Giấy CMND hoặc hộ chiếu (bản photocopy)</div> -->
                                        <!-- </th> -->
                                    </tr>
                                    <tr *ngIf="this.qbhTickAllTPHS == true">
                                        <th></th>
                                        <th class="check-all-checkbox">
                                            <!-- //class ="qbh-mat-checkbox" [ngStyle]="{'margin-left': '106%'}" -->
                                            <mat-checkbox (change)="checkAllCheckBox($event.checked)" 
                                            [checked]="allComplete"
                                            [indeterminate]="someComplete()"> Chọn/Bỏ chọn tất cả
                                            </mat-checkbox> 
                                        </th>
                                    </tr>
                                    <ng-container *ngFor="let form of procedureForm">
                                    <tr class="item table-row mat-cell cdk-cell colorTd cdk-column-code mat-column-code ng-star-inserted">
                                        <td class="center" style="width: 50px;">
                                            <div class="head">{{form.stt}}</div>
                                        </td>
                                        <td>
                                            <div class="head">
                                                <div *ngIf="this.qbhTickAllTPHS == true" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-requirent' : ''">
                                                    <mat-icon [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'requirement_new' : 'requirement'" *ngIf="form.requirement===1">check_circle_outline
                                                    </mat-icon>
                                                    <mat-checkbox color="primary"[checked]="form.autoCheked" (change)="changeParentStatus($event, form.form)" [ngClass]="{'disabledHide': form.requirement == 1}"></mat-checkbox>
                                                    <span>{{ form.form.name }} </span>
                                                </div>
                                                <div *ngIf="this.qbhTickAllTPHS == false" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-requirent' : ''">
                                                    <mat-icon [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'requirement_new' : 'requirement'" *ngIf="form.requirement===1">check_circle_outline
                                                    </mat-icon>
                                                    <mat-checkbox color="primary" [checked]="form.requirement !== -1 || form.autoCheked" (change)="changeParentStatus($event, form.form)" [ngClass]="{'disabledHide': form.requirement == 1}"></mat-checkbox>
                                                    <span>{{ form.form.name }} </span>
                                                </div>
                                                <a (click)="exportEform(form, form.eForm, form.form.name, form.form.id, form.proDef)" class="openEformOnline" *ngIf="form.checkEform == 1 && editRepQBH === false && openDocumentFormNameQni && eformProfileComponent == 0"><span>
                                                    <span class="detailopenEformOnline" *ngIf="!openDocumentFormNameLableQni" i18n>(Click chuột vào đây để mở Biểu mẫu giấy tờ)</span>
                                                    <span class="detailopenEformOnlineQni" *ngIf="openDocumentFormNameLableQni" >{{openDocumentFormNameLableQni}}</span>
                                                    <span class="detailopenEformOnlineQni2" *ngIf="openDocumentFormNameLableQni2" >{{openDocumentFormNameLableQni2}}</span>
                                                </span></a>
                                               <a (click)="exportEform(form, form.eForm, form.form.name, form.form.id, form.proDef)" class="openEformOnline" *ngIf="form.checkEform == 1 && editRepQBH === false && !openDocumentFormNameQni && eformProfileComponent == 0"><span>
                                                <span class="detailopenEformOnline" *ngIf="!openDocumentFormNameLable && cssTextOpenEform == false" i18n>(Click chuột vào đây để mở Biểu mẫu giấy tờ)</span>
                                                <span class="detailopenEformOnline" *ngIf="openDocumentFormNameLable && cssTextOpenEform == false" >{{openDocumentFormNameLable}}</span>
                                                <span class="cssEformOnline" *ngIf="cssTextOpenEform == true">{{openDocumentFormNameLable}}</span>
                                                </span></a>
                                                <a (click)="exportQBHEform(form , form.form.name, form.form.id, form.proDef)" class="openEformOnline" *ngIf="form.checkEform == 1 && editRepQBH === false && !openDocumentFormNameQni && eformProfileComponent == 1"><span>
                                                    <span class="detailopenEformOnline" *ngIf="!openDocumentFormNameLable && cssTextOpenEform == false" i18n>(Click chuột vào đây để mở Biểu mẫu giấy tờ)</span>
                                                    <span class="detailopenEformOnline" *ngIf="openDocumentFormNameLable && cssTextOpenEform == false" >{{openDocumentFormNameLable}}</span>
                                                    <span class="cssEformOnline" *ngIf="cssTextOpenEform == true">{{openDocumentFormNameLable}}</span>
                                                    </span>
                                                </a>
                                            </div>
                                            <btn-get-form-orgin *ngIf="storage468?.getType != 'storage'"
                                                [getFormOrgin]="getFormOrgin"
                                                (afterChoose)="afterChoose($event)"
                                                [form]="form?.form"
                                                [storage468]="storage468">
                                            </btn-get-form-orgin>
                                        </td>
                                        <!-- <td class="body">
                                            <mat-radio-group formControlName="rdo_File">
                                                <ng-container *ngFor="let fd of form.detail; let j = index">
                                                    <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File">
                                                        <mat-radio-button color="primary" value="{{ fd.type.id }}" [checked]="j === 0||fd.autoCheked"  (change)="onRadioChange(form.form.id, fd, $event)">
                                                        </mat-radio-button>
                                                        <mat-form-field appearance="outline" class="quantity" style="width: 60px;">
                                                            <input matInput value="{{ fd.quantity }}" disabled (input)="onQuantityChange(form.form.id, fd, $event.target.value)" type="number" id="quantity_{{form.form.id}}_{{fd.type.id}}">
                                                            </mat-form-field>
                                                    </div>
                                                </ng-container>

                                            </mat-radio-group>
                                        </td> -->
                                        <td class="body" *ngIf="addOtherFile == '0'">
                                            <div class="head">
                                                <mat-radio-group formControlName="rdo_File" >
                                                <ng-container *ngFor="let fd of form.detail; let j = index">
                                                    <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-requirent' : ''">
                                                        <div class="typeName" style="margin-bottom: 5px;">
                                                            <mat-radio-button  *ngIf="checkFormDetail(form.detail)?.length > 1" color="primary" value="{{ fd.type.id }}" [checked]="fd.status === 1" (change)="onRadioChange(form.form.id, fd, $event)">
                                                                <p style="white-space: normal;">{{ fd.quantity }} {{ fd?.type?.id !== '6204d0a679894379eae831c5'? fd.type.name:'Bản điện tử'}}</p>
                                                            </mat-radio-button>  
                                                            <div *ngIf="checkFormDetail(form.detail)?.length <= 1">
                                                                {{ fd.quantity }} {{ fd?.type?.id !== '6204d0a679894379eae831c5'? fd.type.name:'Bản điện tử'}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                </mat-radio-group>
                                            </div>
                                        </td>
                                        <td class="body" *ngIf="addOtherFile == '1'">
                                            <div class="head">
                                                <mat-radio-group formControlName="rdo_File" >
                                                <ng-container *ngFor="let fd of form.detail; let j = index">
                                                    <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-requirent' : ''">
                                                        <div class="typeName" style="margin-bottom: 5px;">
                                                            <mat-radio-button  *ngIf="checkFormDetail(form.detail)?.length > 1" color="primary" value="{{ fd.type.id }}" [checked]="fd.status === 1" (change)="onRadioChange(form.form.id, fd, $event)">
                                                                <p style="white-space: normal;">{{ fd.quantity }} {{ fd?.type?.id !== '6204d0a679894379eae831c5'? fd.type.name:'Bản điện tử'}}</p>
                                                            </mat-radio-button>  
                                                            <div *ngIf="checkFormDetail(form.detail)?.length <= 1" style="display: flex;align-items:center;">
                                                                <mat-form-field appearance="outline" class="quantity" >
                                                                    <input matInput value="{{ fd.quantity }}"
                                                                          (change)="onQuantityChange(form.form.id, fd, $event.target.value)"
                                                                          type="number" id="quantity_{{form.form.id}}_{{fd.type.id}}">
                                                                  </mat-form-field>
                        
                                                                  <mat-form-field appearance="outline" class="typeQBH">
                                                                    <mat-select id="typeqbh_{{form.form.id}}_{{fd.type.id}}" value="{{ fd.type.id }}" (selectionChange)="onTypeQBHChange(form.form.id, fd, $event.value)">
                                                                      <mat-option *ngFor='let type of listTypeQBH;' value="{{type.id}}"> {{ type.name }}</mat-option>
                                                                   </mat-select>
                                                                  </mat-form-field>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                </mat-radio-group>
                                            </div>
                                        </td>
                                        <td class="body" *ngIf="editRepQBH === true">
                                            <div class="head" >
                                                <a (click)="exportQBHEform(form , form.form.name, form.form.id, form.proDef)" class="openEformOnline" *ngIf="form.checkEform == 1"><span>
                                                    <span class="detailopenEformOnline">
                                                     <b style="color:#0018f9;text-decoration:underline;"> Eform</b>
                                                    </span>

                                                </span></a>
                                            </div>
                                        </td>
                                        <td class="body">
                                            <mat-radio-group formControlName="rdo_File">
                                                <div fxLayout="column"  class="divFormFile">
                                                    <div *ngIf="form.file != null && form.file.length >= 1;">
                                                        <div class="setfileForm" *ngIf="form.file != null && form.file.length > 1">
                                                            <mat-form-field appearance="fill" class="selectFileTemplate setfileForm" fxFlex="93"
                                                                            [style.z-index]="form.file.length > 0 ? 1 : -1">
                                                                <mat-select placeholder="Xem mẫu đơn">
                                                                    <mat-option *ngFor="let file of form.file" value="{{ file.id }}"
                                                                                (click)="downloadFile(file.id, file.filename,file.uuid)">
                                                                        {{ file.filename }}
                                                                    </mat-option>
                                                                </mat-select>
                                                            </mat-form-field>
                                                        </div>
                                                        <div class="setfileForm" *ngIf="form.file != null && form.file.length == 1">
                                                            <div class="file" *ngFor="let file of form.file" (click)="downloadFile(file.id, file.filename,file.uuid)" style="cursor:pointer" i18n-tooltip #tooltip="matTooltip" matTooltip="Tải xuống tệp tin">
                                                                <div class="icon" [ngStyle]="{'background-image': 'url('+ getIconFilename(file)  +')'}"> </div>
                                                                <div class="name">{{ file.filename }}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div *ngIf="form.fileLink != null && form.fileLink != ''" class="linkfileForm" fxLayout="row">
                                                        <a href="{{form.fileLink}}" target="_blank">{{form?.fileLinkName ? form?.fileLinkName : 'Link file mẫu'}}</a>
                                                    </div>
                                                    <div *ngIf="form.fileLink == null || form.fileLink == ''" class="linkfileForm" fxLayout="row">
                                                        <p style="color: white;">...............</p>
                                                    </div>
                                                </div>
                                            </mat-radio-group>
                                        </td>
                                        <td class="body" style="max-width: 30px" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowQuantityElectronicAuthentication" >
                                            <mat-radio-group formControlName="rdo_File">
                                                <div *ngFor="let fd of form.detail; let j = index">
                                                    <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0"
                                                         fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                                        <div class="listLoaiChungThuc" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                                <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                                    <mat-form-field appearance="outline">
                                                                        <!-- <mat-label>Số bản</mat-label> -->
                                                                        <input matInput value="{{fd?.quantity ? fd?.quantity : 1}}"
                                                                            type="number" min="1" (input)="onQuantityChange(form.form.id, fd, $event.target.value)">
                                                                    </mat-form-field>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </mat-radio-group>
                                        </td>
                                        <td class="body" style="max-width: 30px" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">
                                            <mat-radio-group formControlName="rdo_File">
                                              <div *ngFor="let fd of form.detail; let j = index">
                                                <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0"
                                                      fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                                  <div class="listLoaiChungThuc" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                      <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                        <mat-form-field appearance="outline">
                                                          <!-- <mat-label>Số trang</mat-label> -->
                                                          <input matInput value="{{form.authentication?.pageNumber ? form.authentication?.pageNumber : 1}}"
                                                              type="number" min="1" (input)="onPageNumberAuthChange(form.form.id, fd, $event.target.value)">
                                                        </mat-form-field>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </mat-radio-group>
                                        </td>
                                        <td class="body" style="max-width: 30px" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowCopyNumberElectronicAuthentication" >
                                            <mat-radio-group formControlName="rdo_File">
                                                <div *ngFor="let fd of form.detail; let j = index">
                                                    <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0"
                                                         fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                                        <div class="listLoaiChungThuc" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                                <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                                    <mat-form-field appearance="outline">
                                                                        <!-- <mat-label>Số bản sao</mat-label> -->
                                                                        <input matInput value="{{form.authentication?.copiesAmount ? form.authentication?.copiesAmount : 1}}"
                                                                               type="number" min="1" (input)="onCopiesAmountAuthChange(form.form.id, fd, $event.target.value)">
                                                                    </mat-form-field>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </mat-radio-group>
                                        </td>
                                        <td class="body" *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">
                                            <mat-radio-group formControlName="rdo_File">
                                              <div *ngFor="let fd of form.detail; let j = index">
                                                <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0"
                                                      fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                                  <div class="listLoaiChungThuc" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                      <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                        <mat-form-field appearance="outline">
                                                          <!-- <mat-label>Loại chứng thực</mat-label> -->
                                                          <mat-select value="{{form.authentication?.type.id ? form.authentication.type.id : 0}}"
                                                           (selectionChange)="authenticationTypeChanged($event, form.form)">
                                                            <mat-option *ngFor='let element of listAuthenticationType' value="{{element.id}}">
                                                              {{ element.name }}
                                                            </mat-option>
                                                          </mat-select>
                                                        </mat-form-field>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </mat-radio-group>
                                        </td>
                                        <td class="body">
                                            <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'checkbox-container-new' : 'checkbox-container'" *ngIf="form.requirement===1 && formFileDirect.enable">
                                                <mat-checkbox [checked]="form.directCheck" [disabled]="form.fileCount > 0" (change)="onCheckDirectFormFile($event, form.form)"></mat-checkbox>
                                                <span>{{formFileDirect.description}}</span>
                                            </div>
                                            <mat-radio-group formControlName="rdo_File">
                                                <ng-container *ngFor="let fd of form.detail; let j = index">
                                                    <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File">
                                                        <div class="listUploadedFile" *ngIf="fd.status === 1">
                                                            <div class="wrapList"
                                                                fxLayoutAlign="space-evenly">
                                                                <div *ngFor="let detailFile of fd.file; let f = index;" class="file" fxFlex.gt-sm='100' fxFlex.gt-xs="grow" fxFlex='grow'>
                                                                    <mat-icon class="icon">insert_drive_file</mat-icon>
                                                                    <span class="name">{{detailFile.filename ? detailFile.filename : detailFile.name}}</span>
                                                                    <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="fileMenuOption">
                                                                        <mat-icon>more_vert</mat-icon>
                                                                    </button>
                                                                    <mat-menu #fileMenuOption="matMenu">
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="removeFile(form.id, form.form.id, fd.type.id,false,f, true, form)">
                                                                            <mat-icon>close</mat-icon>
                                                                            <span>Xóa</span>
                                                                        </button>
                                                                        <a mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'"
                                                                           target="_blank"
                                                                           [routerLink]="routerLink(detailFile)">
                                                                          <mat-icon>format_size</mat-icon>
                                                                          <span>Xem trước</span>
                                                                        </a>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="downloadFile(detailFile.id, detailFile.filename, detailFile.uuid)">
                                                                            <mat-icon>download</mat-icon>
                                                                            <span>Tải xuống tệp tin</span>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openPdfDigitalSignature(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid, 1)" *ngIf="digitalSignature.VNPTSim">
                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                            <span>Ký số sim</span>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openPdfDigitalSignature(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid)" *ngIf="digitalSignature.SmartCA">
                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                            <span>Ký số Smart CA</span>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openVGCAplugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, detailFile.uuid)" *ngIf="digitalSignature.VGCA">
                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                            <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                                                            <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                                                            <ng-template #elseBlock>Ký số Token</ng-template>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openVnptCaPlugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, detailFile.uuid)" *ngIf="digitalSignature.VNPTCA">
                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                            <span>Ký số VNPT-CA</span>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openViettelCaPlugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, form, fd, detailFile, detailFile.uuid)" *ngIf="digitalSignature.ViettelCA">
                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                            <span i18n="@@signViettelCA">Ký số Viettel-CA</span>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openViettelSim(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, form, fd, detailFile, detailFile.uuid)" *ngIf="digitalSignature.ViettelSim">
                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                            <span i18n="@@signViettelSim">Ký số sim Viettel</span>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openNEAC(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid, 5)" *ngIf="digitalSignature.NEAC">
                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                            <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                                                        </button>
                                                                        <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'">
                                                                          <view-sign-history [fileId]="detailFile.id">
                                                                            <mat-icon>refresh</mat-icon>
                                                                            <span>Xem lịch sử ký số</span>
                                                                          </view-sign-history>
                                                                        </button>
                                                                    </mat-menu>
                                                                </div>
                                                                <div fxLayout="row" fxLayoutAlign="space-between" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'uploadBtnNew' : 'uploadBtn'" fxFlex='grow' *ngIf="displayScanFilePadsvc">
                                                                    <button mat-button type="button" class="btn_upload"
                                                                            (click)="getInfo(form, fd.type, form.proDef); scanToPdfWithThumbnails()">
                                                                        <mat-icon>scanner</mat-icon>
                                                                        <span class="text">Scan tệp tin</span>
                                                                    </button>
                                                                </div>
                                                                 <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'uploadBtnNew' : 'uploadBtn'" fxFlex='grow'>
                                                                    <ng-container
                                                                    *ngIf="editRepQBH === false; then Normal else QBH">
                                                                  </ng-container>
                                                                  <ng-template #Normal>
                                                                    <button mat-button class="btn_upload">
                                                                        <mat-icon>attachment</mat-icon>
                                                                        <span>Chọn tệp tin</span>
                                                                    </button>
                                                                    <div *ngIf="env?.enableFileFormat === true && form?.isPicture === true ">
                                                                        <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="fileFormat" value="{{blankVal}}">
                                                                    </div>
                                                                    <div *ngIf="env?.enableFileFormat !== true || form?.isPicture !== true">
                                                                        <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="listAcceptExt" value="{{blankVal}}">
                                                                    </div>
                                                                    <div class="text-center" *ngIf="showMaxFileSize && !(configMaxSizeFileHCM && valueMaxSizeFileHCM)">Kích thước tối đa của tệp tin {{maxFileSize}} MB</div>
                                                                    <div class="text-center" *ngIf="configMaxSizeFileHCM && valueMaxSizeFileHCM">Kích thước tối đa của tệp tin {{valueMaxSizeFileHCM}} MB</div>
                                                                    </ng-template>
                                                                    <ng-template #QBH>
                                                                        <ng-container
                                                                        *ngIf="form.checkEform == 1; then lock else unlock">
                                                                      </ng-container>
                                                                      <ng-template #lock>
                                                                        <button  mat-button class="btn_upload" >
                                                                            <mat-icon>attachment</mat-icon>
                                                                            <!-- <span>Nhập eform để đính kèm</span> -->
                                                                            <span>Chọn tệp tin</span>
                                                                        </button>
                                                                        <div *ngIf="env?.enableFileFormat === true && form?.isPicture === true ">
                                                                            <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="fileFormat" value="{{blankVal}}">
                                                                        </div>
                                                                        <div *ngIf="env?.enableFileFormat !== true || form?.isPicture !== true">
                                                                            <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="listAcceptExt" value="{{blankVal}}">
                                                                        </div>
                                                                        <div class="text-center" *ngIf="showMaxFileSize">Kích thước tối đa của tệp tin {{maxFileSize}} MB</div>
                                                                        </ng-template>
                                                                        <ng-template #unlock>
                                                                            <button mat-button class="btn_upload">
                                                                                <mat-icon>attachment</mat-icon>
                                                                                <span>Chọn tệp tin</span>
                                                                            </button>
                                                                            <div *ngIf="env?.enableFileFormat === true && form?.isPicture === true ">
                                                                                <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="fileFormat" value="{{blankVal}}">
                                                                            </div>
                                                                            <div *ngIf="env?.enableFileFormat !== true || form?.isPicture !== true">
                                                                                <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="listAcceptExt" value="{{blankVal}}">
                                                                            </div>
                                                                            <div class="text-center" *ngIf="showMaxFileSize">Kích thước tối đa của tệp tin {{maxFileSize}} MB</div>
                                                                         </ng-template>

                                                                     </ng-template>

                                                                </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                </ng-container>
                                            </mat-radio-group>
                                        </td>
                                        <!-- <td>
                                            <btn-get-form-orgin
                                                [getFormOrgin]="getFormOrgin"
                                                (afterChoose)="afterChoose($event)"
                                                [form]="form?.form"
                                                [storage468]="storage468">
                                            </btn-get-form-orgin>
                                        </td> -->
                                    </tr>
                                    <tr *ngIf="form.form && form.form.id && selectedFormId === form.form.id && eformProfileComponent == 1">
                                        <td colspan="100%">
                                            <div class="eform-container" #eformContainer [attr.data-form-id]="form.form.id">
                                            </div>
                                        </td>
                                    </tr>
                                    </ng-container>
                                </table>
                                <a *ngIf="canHideAddNewFileBtn == false || enableAddNewDocument == true" mat-stroked-button
                                    class="btn_addNewFile" (click)="addForm()">
                                    <mat-icon>add</mat-icon>
                                    <span i18n>Thêm giấy tờ</span>
                                </a>
                            </div>
                            <!--end-->
                            <div *ngIf="!procedureFormType || procedureFormType == 0">
                                <div class="item" *ngFor="let form of procedureForm" [ngClass]="{'disabledHide': form.procedureProcessDefinition != null && selectedProcess.id != form.procedureProcessDefinition.id}">
                                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                                        <div class="head" fxFlex='grow' fxLayout="row" fxLayoutAlign="space-between" fxLayout.xs="column" fxLayout.sm="column">
                                            <div>
                                                <mat-icon [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'requirement_new' : 'requirement'" *ngIf="form.requirement===1">check_circle_outline
                                                </mat-icon>
                                                <mat-checkbox color="primary" [checked]="form.requirement !== -1||form.autoCheked" (change)="changeParentStatus($event, form.form)" [ngClass]="{'disabledHide': form.requirement == 1}"></mat-checkbox>
                                                <span>{{ form.form.name }}</span>
                                            </div>
                                            <a (click)="exportEform(form, form.eForm, form.form.name, form.form.id, form.proDef)" class="openEformOnline" *ngIf="form.checkEform == 1 && openDocumentFormNameQni"><span>
                                                <span class="detailopenEformOnline" *ngIf="!openDocumentFormNameLableQni" i18n>(Click chuột vào đây để mở Biểu mẫu giấy tờ)</span>
                                                <span class="detailopenEformOnlineQni" *ngIf="openDocumentFormNameLableQni" >{{openDocumentFormNameLableQni}}</span>
                                                <span class="detailopenEformOnlineQni2" *ngIf="openDocumentFormNameLableQni2" >{{openDocumentFormNameLableQni2}}</span>
                                            </span></a>
                                            <a (click)="exportEform(form, form.eForm, form.form.name, form.form.id, form.proDef)" class="openEformOnline" *ngIf="form.checkEform == 1 && !openDocumentFormNameQni"><span>
                                                <span class="detailopenEformOnline" *ngIf="!openDocumentFormNameLable" i18n>(Click chuột vào đây để mở Biểu mẫu giấy tờ)</span>
                                                <span class="detailopenEformOnline" *ngIf="openDocumentFormNameLable" >{{openDocumentFormNameLable}}</span>
                                            </span></a>
                                        </div>
                                    </div>
                                    <div class="body">
                                        <mat-radio-group formControlName="rdo_File">
                                          <ng-container *ngFor="let fd of form.detail; let j = index">
                                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                              <mat-radio-button color="primary" value="{{ fd.type.id }}" [checked]="fd.status === 1" fxFlex.gt-sm='3' (change)="onRadioChange(form.form.id, fd, $event)">
                                              </mat-radio-button>
                                              <div fxFlex.gt-sm='10' class="divFormFile">
                                                <mat-form-field appearance="outline" class="quantity" >
                                                  <input matInput value="{{ fd.quantity }}" (input)="onQuantityChange(form.form.id, fd, $event.target.value)" type="number" id="quantity_{{form.form.id}}_{{fd.type.id}}">
                                                </mat-form-field>
                                              </div>

                                              <div class="typeName" fxFlex.gt-sm='14' fxFlex="45">
                                                {{ fd?.type?.id !== '6204d0a679894379eae831c5'? fd.type.name:'Bản điện tử'}}
                                              </div>

                                              <!-- <mat-form-field appearance="fill" class="selectFileTemplate" fxFlex.gt-sm='20' fxFlex="93" [style.z-index]="form.file.length > 0 ? 1 : -1" *ngIf="form.file !== null && form.file.length > 1">
                                                  <mat-select i18n-placeholder placeholder="Chọn mẫu đơn">
                                                      <mat-option *ngFor="let file of form.file" value="{{ file.id }}" (click)="downloadFile(file.id, file.filename)">
                                                          {{ file.filename }}
                                                      </mat-option>
                                                  </mat-select>
                                              </mat-form-field>
                                              <div fxFlex.gt-sm='20' fxFlex="93" fxFlex='grow' *ngIf="form.file !== null && form.file.length === 1">
                                                  <label i18n>Mẫu đơn</label>
                                                  <div class="file" *ngFor="let file of form.file" (click)="downloadFile(file.id, file.filename)" style="cursor:pointer" i18n-tooltip #tooltip="matTooltip" matTooltip="Tải xuống tệp tin">
                                                      <div class="icon" [ngStyle]="{'background-image': 'url('+ getIconFilename(file)  +')'}"> </div>
                                                      <div class="name">{{ file.filename }}</div>
                                                  </div>
                                              </div> -->
                                              <div fxLayout="column" fxFlex.gt-sm='20' fxFlex="93" class="divFormFile">
                                                <div class="setfileForm" *ngIf="form.file!= null && form.file.length >= 1;">
                                                  <mat-form-field appearance="fill" class="selectFileTemplate setfileForm"
                                                                  *ngIf="form.file !== null && form.file.length > 1" fxFlex="93"
                                                                  [style.z-index]="form.file.length > 0 ? 1 : -1">
                                                    <mat-select placeholder="Xem mẫu đơn">
                                                      <mat-option *ngFor="let file of form.file" value="{{ file.id }}"
                                                                  (click)="downloadFile(file.id, file.filename, file.uuid)">
                                                        {{ file.filename }}
                                                      </mat-option>
                                                    </mat-select>
                                                  </mat-form-field>
                                                </div>
                                                <div class="setfileForm" *ngIf="form.file !== null && form.file.length === 1">
                                                  <label i18n>Mẫu đơn</label>
                                                  <div class="file" *ngFor="let file of form.file" (click)="downloadFile(file.id, file.filename, file.uuid)" style="cursor:pointer" i18n-tooltip #tooltip="matTooltip" matTooltip="Tải xuống tệp tin">
                                                    <div class="icon" [ngStyle]="{'background-image': 'url('+ getIconFilename(file)  +')'}"> </div>
                                                    <div class="name">{{ file.filename }}</div>
                                                  </div>
                                                </div>
                                                <div *ngIf="form.fileLink != null && form.fileLink != ''" class="linkfileForm" fxLayout="row">
                                                  <a href="{{form.fileLink}}" target="_blank" *ngIf="form.fileLink != null && form.fileLink != ''">{{form?.fileLinkName ? form?.fileLinkName : 'Link file mẫu'}}</a>
                                                </div>
                                              </div>
                                              <div fxFlex="2"></div>

                                              <!-- <div class="uploadFile file_upload_{{form.proDef}}_{{form.form.id}}_{{fd.type.id}}" fxFlex.gt-sm='40' fxFlex="grow">
                                                  <button mat-button>
                                                      <mat-icon>attachment</mat-icon>
                                                      <span i18n>Chọn tệp tin</span>
                                                  </button>
                                                  <input id="file_upload" type='file' (change)="onFileSelected($event, form.form.id, fd.type.id, form.proDef)" [accept]="listAcceptExt" value="{{blankVal}}">
                                              </div> -->

                                              <!-- <div class="listUploadedFile" fxFlex.gt-sm='40' fxFlex.gt-xs="grow" fxFlex='grow'>
                                                <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row"
                                                     fxLayoutAlign="space-evenly">
                                                  <div *ngFor="let detailFile of fd.file; let f = index;" class="file" fxFlex.gt-sm="49" fxFlex.gt-xs="49" fxFlex='grow'>
                                                    <mat-icon class="icon">insert_drive_file</mat-icon>
                                                    <span class="name">{{detailFile.filename ? detailFile.filename : detailFile.name}}</span>
                                                    <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="fileMenuOption">
                                                      <mat-icon>more_vert</mat-icon>
                                                    </button>
                                                    <input id="file_upload" type='file' (change)="onFileSelected($event, form.form.id, fd.type.id, form.proDef)" [accept]="listAcceptExt" value="{{blankVal}}">
                                                </div> -->

                                                <div class="listLoaiChungThuc" fxFlex.gt-sm='6' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowQuantityElectronicAuthentication">
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                        <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                            <mat-form-field appearance="outline">
                                                                <mat-label>Số bản</mat-label>
                                                                <input matInput value="{{form.detail?.quantity ? form.detail?.quantity : 1}}"
                                                                       type="number" min="1"  (input)="onQuantityChange(form.form.id, fd, $event.target.value)">
                                                            </mat-form-field>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="listLoaiChungThuc" fxFlex.gt-sm='6' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                      <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                        <mat-form-field appearance="outline">
                                                          <mat-label>Số trang</mat-label>
                                                          <input matInput value="{{form.authentication?.pageNumber ? form.authentication?.pageNumber : 1}}"
                                                             type="number" min="1" (input)="onPageNumberAuthChange(form.form.id, fd, $event.target.value)">
                                                        </mat-form-field>
                                                      </div>
                                                    </div>
                                                  </div>
                                                
                                                <div class="listLoaiChungThuc" fxFlex.gt-sm='6' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT && enableShowCopyNumberElectronicAuthentication">
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                        <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                            <mat-form-field appearance="outline">
                                                                <mat-label>Số bản sao</mat-label>
                                                                <input matInput value="{{form.authentication?.copiesAmount ? form.authentication?.copiesAmount : 1}}"
                                                                       type="number" min="1"  (input)="onCopiesAmountAuthChange(form.form.id, fd, $event.target.value)">
                                                            </mat-form-field>
                                                        </div>
                                                    </div>
                                                </div>
  
                                                  <div class="listLoaiChungThuc" fxFlex.gt-sm='15' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcessFromUrl?.isCTDT">
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                      <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                                        <mat-form-field appearance="outline">
                                                          <mat-label>Loại chứng thực</mat-label>
                                                          <mat-select value="{{form.authentication?.type.id ? form.authentication.type.id : defaultAuthTypeId}}"
                                                             (selectionChange)="authenticationTypeChanged($event, form.form)">
                                                            <mat-option *ngFor='let element of listAuthenticationType' value="{{element.id}}">
                                                              {{ element.name }}
                                                            </mat-option>
                                                          </mat-select>
                                                        </mat-form-field>
                                                      </div>
                                                    </div>
                                                  </div>

                                                <div class="listUploadedFile" fxFlex.gt-sm='{{flexGtSmColumnListUploadedFile}}' fxFlex.gt-xs="grow" fxFlex='grow'>
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row"
                                                        fxLayoutAlign="space-evenly">
                                                        <div *ngFor="let detailFile of fd.file; let f = index;" class="file" fxFlex.gt-sm="49" fxFlex.gt-xs="49" fxFlex='grow'>
                                                            <mat-icon class="icon">insert_drive_file</mat-icon>
                                                            <span class="name">{{detailFile.filename ? detailFile.filename : detailFile.name}}</span>
                                                            <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="fileMenuOption">
                                                                <mat-icon>more_vert</mat-icon>
                                                            </button>
                                                            <mat-menu #fileMenuOption="matMenu">
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="removeFile(form.id, form.form.id, fd.type.id,false,f, true)">
                                                                    <mat-icon>close</mat-icon>
                                                                    <span>Xóa</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="downloadFile(detailFile.id, detailFile.filename, detailFile.uuid)">
                                                                    <mat-icon>download</mat-icon>
                                                                    <span>Tải xuống tệp tin</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openPdfDigitalSignature(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid,1)" *ngIf="digitalSignature.VNPTSim">
                                                                    <mat-icon class="mainColor">verified</mat-icon>
                                                                    <span>Ký số sim</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openPdfDigitalSignature(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid)" *ngIf="digitalSignature.SmartCA">
                                                                    <mat-icon class="mainColor">verified</mat-icon>
                                                                    <span>Ký số Smart CA</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openVGCAplugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, detailFile.uuid)" *ngIf="digitalSignature.VGCA">
                                                                    <mat-icon class="mainColor">verified</mat-icon>
                                                                    <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                                                    <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                                                    <ng-template #elseBlock>Ký số Token</ng-template>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openVnptCaPlugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, detailFile.uuid)" *ngIf="digitalSignature.VNPTCA">
                                                                    <mat-icon class="mainColor">verified</mat-icon>
                                                                    <span>Ký số VNPT-CA</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openViettelCaPlugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, form, fd, detailFile, detailFile.uuid)" *ngIf="digitalSignature.ViettelCA">
                                                                    <mat-icon class="mainColor">verified</mat-icon>
                                                                    <span i18n="@@signViettelCA">Ký số Viettel-CA</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openViettelSim(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, form, fd, detailFile, detailFile.uuid)" *ngIf="digitalSignature.ViettelSim">
                                                                    <mat-icon class="mainColor">verified</mat-icon>
                                                                    <span i18n="@@signViettelSim">Ký số sim Viettel</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openNEAC(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid, 5)" *ngIf="digitalSignature.NEAC">
                                                                    <mat-icon class="mainColor">verified</mat-icon>
                                                                    <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                                                </button>
                                                                <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'">
                                                                    <view-sign-history [fileId]="detailFile.id">
                                                                       <mat-icon>refresh</mat-icon>
                                                                       <span>Xem lịch sử ký số</span>
                                                                    </view-sign-history>
                                                              </button>
                                                            </mat-menu>
                                                        </div>
                                                        <div fxLayout="row" fxLayoutAlign="space-between" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'uploadBtnNew' : 'uploadBtn'" fxFlex='grow' *ngIf="displayScanFilePadsvc">
                                                            <button mat-button type="button" class="btn_upload"
                                                                    (click)="getInfo(form, fd.type, form.proDef); scanToPdfWithThumbnails()">
                                                                <mat-icon>scanner</mat-icon>
                                                                <span class="text">Scan tệp tin</span>
                                                            </button>
                                                        </div>
                                                        <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'uploadBtnNew' : 'uploadBtn'" fxFlex='grow'
                                                             [fxFlex.gt-sm]="fd.file.length % 2 != 0?49:98"
                                                             [fxFlex.gt-xs]="fd.file.length % 2 != 0?49:98">
                                                          <button mat-button class="btn_upload">
                                                            <mat-icon>attachment</mat-icon>
                                                            <span>Chọn tệp tin</span>
                                                        </button>
                                                        <div *ngIf="env?.enableFileFormat === true && form?.isPicture === true ">
                                                            <input multiple type="file" (change)="onSelectFile($event, form, fd.type)" [accept]="fileFormat" value="{{blankVal}}">
                                                        </div>
                                                        <div *ngIf="env?.enableFileFormat !== true || form?.isPicture !== true ">
                                                            <input multiple type="file" (change)="onSelectFile($event, form, fd.type)" [accept]="listAcceptExt" value="{{blankVal}}">
                                                        </div>
                                                        <div class="text-center" *ngIf="showMaxFileSize">Kích thước tối đa của tệp tin {{maxFileSize}} MB</div>
                                                    </div>
                                                    <!-- <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'uploadBtnNew' : 'uploadBtn'" fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'
                                                        *ngIf="fd.file.length % 2 == 0">
                                                        <button mat-button class="btn_upload">
                                                            <mat-icon>attachment</mat-icon>
                                                            <span>Chọn tệp tin</span>
                                                        </button>
                                                        <input multiple type="file"  (change)="onSelectFile($event, form, fd.type)" [accept]="listAcceptExt" value="{{blankVal}}">
                                                    </div> -->
                                              </div>
                                              </div>
                                            </div>
                                        </ng-container>

                                        </mat-radio-group>
                                    </div>
                                    <btn-get-form-orgin *ngIf="storage468?.getType != 'storage'"
                                        [getFormOrgin]="getFormOrgin"
                                        (afterChoose)="afterChoose($event)"
                                        [form]="form?.form"
                                        [storage468]="storage468">
                                    </btn-get-form-orgin>
                                </div>
                                <a *ngIf="canHideAddNewFileBtn == false || enableAddNewDocument == true" mat-stroked-button
                                  class="btn_addNewFile" (click)="addForm()">
                                    <mat-icon>add</mat-icon>
                                    <span i18n>Thêm giấy tờ</span>
                                </a>
							</div>
                        </form>
                    </div>
                    <br>
                    <div class="ctrl" style="display: flex; justify-content: center;">
                        <get-form-origin-item *ngIf="storage468?.getType == 'storage'"
                            (afterChoose)="afterChoose($event)"
                            [procedureId]="this.procedureId"
                            [userId]="this.userInfo.id"
                            [fullname]="this.applicantEForm?.data?.data[fullname]"
                            [identityNumber]="this.applicantEForm?.data?.data[identityNumberKey]"
                            [taxcode]="this.applicantEForm?.data?.data[taxCodeKey]"
                            [ownerFullname]="this.applicantEForm?.data?.data.ownerFullname"
                            [elementProcedure] = "this.listStatusProcedureForm"
                            [formProcedure] = "this.procedureForm"
                            (selectFileEvent)="onSelectFile($event.event, $event.detailID, $event.typeID)"
                            [resultCode]="this.listResultCode">
                        </get-form-origin-item>
                        <button mat-stroked-button (click)="goBack(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" i18n *ngIf="!changeStepApplyDossier">Quay lại</button>
                        <button mat-flat-button (click)="confirmApplyS2(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n *ngIf="!changeStepApplyDossier">Tiếp tục</button>
                    </div>
                    <ng-container *ngIf="changeStepApplyDossier">
                        <div class="ctrl">
                            <div class="captcha">
                                <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'">
                                    <mat-checkbox (change)="changeConfirmStatus($event)">
                                        <span i18n>Tôi chắc chắn rằng các thông tin khai báo trên là đúng sự thật và đồng ý chịu trách nhiệm trước pháp luật về lời khai trên.</span>
                                    </mat-checkbox>
                                </div>
                                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="captchaImgage formFieldOutline" *ngIf="this.captcharOpen == 1">
                                    <form [formGroup]="captchaForm" (submit)="captchaSubmit()" id="captchaForm" fxFlex='20' fxLayout="row">
                                        <mat-form-field appearance="outline" fxFlex='grow'>
                                            <mat-label i18n>Mã xác nhận</mat-label>
                                            <input matInput formControlName="captcha" required>
                                            <mat-error *ngIf="captcha.invalid" class="error_Msg">
                                                <span i18n>Vui lòng nhập mã xác nhận</span>
                                                <div class="err">
                                                    <mat-icon>priority_high</mat-icon>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                    </form>
                                    <div fxFlex='8' class="chCode">
                                        <div [innerHTML]="captchaImg" class="chIMG" (click)="generateCaptcha()"></div>
                                        <button mat-flat-button (click)="generateCaptcha()">
                                            <mat-icon>cached</mat-icon>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <button mat-stroked-button (click)="goBack(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" i18n>Quay lại</button>
                            <button mat-flat-button (click)="confirmPay(stepper); captchaSubmit()" form="captchaForm" *ngIf="isConfirm" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n="@@submitApplyDossier">Nộp hồ sơ</button>
                        </div>
                    </ng-container>
                </mat-step>
                <mat-step i18n-label label="Thành phần hồ sơ" *ngIf="OS_HGI==true" >
                    <div *ngIf="OS_HGI && supportTextApplyDossier.isEnabled && supportTextApplyDossier.profilecomposition.isEnabled" 
                    style="width: 100%; background-color: rgba(0, 0, 0, 0.03); height: fit-content; margin: auto; padding: 15px; margin-bottom: 10px;"> 
                        <p [innerHTML]="supportTextApplyDossier.profilecomposition.title"></p>
                        <div [innerHTML]="supportTextApplyDossier.profilecomposition.content"></div>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div class="s_head">
                        <span i18n>Thành phần hồ sơ</span>
                    </div>
                    <div *ngIf="showNoteDocumentEnable === 1" class="mt-1" style="font-style:italic">Ghi chú: <span style="color: #de1212;">{{showNoteDocumentValue}}</span></div>
                    <div *ngIf="showNoteProfileComposition && procedureForm.length != 0 && !isNoForm" class="note-profile-composition" >
                        <span i18n="@@noteProfileCompositionNote">Ghi chú:</span>&nbsp;
                        <span class="material-icons custom-icon-note">check_circle_outline</span>
                        <span class="text-note-color" i18n="@@noteProfileCompositionTitle">Giấy tờ bắt buộc đính kèm</span>&nbsp;
                        <span i18n="@@noteProfileCompositionDescribe">(Khuyến khích đính kèm file scan dạng .pdf)</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div class="success_head" *ngIf="procedureForm.length == 0 || isNoForm">
                        <p class="title" i18n>Không có thông tin giấy tờ</p>
                    </div>
                    <div class="procedureFormHGI" *ngIf="procedureForm.length != 0 && !isNoForm">
                        <get-form-orgin
                            [fullname]="applicantEForm?.data?.data?.fullname"
                            [identityNumber]="applicantEForm?.data?.data?.identityNumber"
                            [birthday]="applicantEForm?.data?.data?.birthday"
                            #getFormOrgin>
                        </get-form-orgin>
                        <form [formGroup]="prForm">
                             <!--Thêm loại procedureFormType hiển thị dạng bảng-->
                             <div *ngIf="procedureFormType && procedureFormType == 1">
                                <br />
                                <div class="table-container" style="box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);">
                                    <table mat-table [dataSource]="procedureForm" multiTemplateDataRows  class="responsive-table mat-elevation-z8">
                                        <ng-container matColumnDef="stt">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg" style="justify-content: center;">STT</mat-header-cell>
                                            <mat-cell *matCellDef="let row; let i = dataIndex" data-label="" class="text-center" style="font-weight: 500; font-size: 15px; justify-content: center;">{{ i + 1}}</mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="tengiayto">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg" style="justify-content: center;">Tên giấy tờ</mat-header-cell>
                                            <mat-cell *matCellDef="let row" data-label="" >
                                                <div class="head">
                                                    <div>
                                                        <mat-icon [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'requirement_new' : 'requirement'" *ngIf="row.requirement===1">check_circle_outline
                                                        </mat-icon>
                                                        <span style="padding: 0; font-weight: 500; font-size: 15px; padding-top: 0.1em;">{{ row.form.name }}</span>
                                                    </div>
                                                </div>
                                                <btn-get-form-orgin [getFormOrgin]="getFormOrgin" (afterChoose)="afterChoose($event)" [form]="row?.form" [storage468]="storage468">
                                                </btn-get-form-orgin>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="loaiban">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg" style="justify-content: center;">Loại bản</mat-header-cell>
                                            <mat-cell *matCellDef="let row" data-label="" style="font-size: 15px;">
                                                <div class="head">
                                                    <ng-container *ngFor="let fd of row.detail; let j = index">
                                                        <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" >
                                                            <div class="typeName">
                                                                {{ fd.quantity }} {{ fd?.type?.id !== '6204d0a679894379eae831c5'? fd.type.name:'Bản điện tử'}}
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </div>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="bieumaudientu">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg" style="justify-content: center;"></mat-header-cell>
                                            <mat-cell *matCellDef="let row" data-label="">
                                                <button mat-stroked-button style="background-color: #ce7a58;  color: white; font-size: 16px;  min-height: 42px; width: 100%; border: 1px solid; word-wrap: break-word; white-space: normal; border-radius: 4px; line-height: 20px; padding: 5px;" *ngIf="row.checkEform == 1"
                                                (click)="expandRow(row)"  #tooltip="matTooltip" matTooltip="Điền biểu mẫu điện tử và ký số, hệ thống tự động đính kèm thành phần hồ sơ sau khi hoàn tất.">
                                                <mat-icon>insert_drive_file</mat-icon>&nbsp;<span>Điền biểu mẫu điện tử và ký số</span></button>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="hoac">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg"></mat-header-cell>
                                            <mat-cell *matCellDef="let row" data-label="" style="text-align: center;"><p *ngIf="row.checkEform == 1" style="font-size: 15px; margin: auto;">HOẶC</p></mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="dinhkemgiayto">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg" style="justify-content: center;">Đính kèm giấy tờ</mat-header-cell>
                                            <mat-cell *matCellDef="let form" data-label="" style="padding-left: 5px; padding-right: 5px; width: 100%;">
                                                <mat-radio-group formControlName="rdo_File" style="max-width: 100% !important; overflow: hidden !important;width: 100%;">
                                                    <ng-container *ngFor="let fd of form.detail; let j = index">
                                                        <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File">
                                                            <div class="listUploadedFile">
                                                                <div class="wrapList" fxLayoutAlign="space-evenly">
                                                                    <div *ngFor="let detailFile of fd.file; let f = index;" class="file" fxFlex.gt-sm='100' fxFlex.gt-xs="grow" fxFlex='grow'>
                                                                        <mat-icon class="icon">insert_drive_file</mat-icon>
                                                                        <span class="name">{{detailFile.filename ? detailFile.filename : detailFile.name}}</span>
                                                                        <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="fileMenuOption">
                                                                            <mat-icon>more_vert</mat-icon>
                                                                        </button>
                                                                        <mat-menu #fileMenuOption="matMenu">
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="removeFile(form.id, form.form.id, fd.type.id,false,f, true)">
                                                                                <mat-icon>close</mat-icon>
                                                                                <span>Xóa</span>
                                                                            </button>
                                                                            <a mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" target="_blank" [routerLink]="routerLink(detailFile)">
                                                                                <mat-icon>format_size</mat-icon>
                                                                                <span>Xem trước</span>
                                                                            </a>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="downloadFile(detailFile.id, detailFile.filename)">
                                                                                <mat-icon>download</mat-icon>
                                                                                <span>Tải xuống tệp tin</span>
                                                                            </button>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openNEAC(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid, 5)" *ngIf="digitalSignature.NEAC">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                                                            </button>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openPdfDigitalSignature(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid)" *ngIf="digitalSignature.SmartCA">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span>Ký số Smart CA</span>
                                                                            </button>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openPdfDigitalSignature(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid,1)" *ngIf="digitalSignature.VNPTSim">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span>Ký số sim</span>
                                                                            </button>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openVnptCaPlugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, detailFile.uuid)" *ngIf="digitalSignature.VNPTCA">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span *ngIf="!OS_BDG">Ký số VNPT-CA</span>
                                                                                <!-- <span *ngIf="OS_BDG">Ký số VNPT-CA (Token)</span> -->
                                                                                <span *ngIf="OS_BDG">Ký số USB</span>
                                                                            </button>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openVGCAplugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, detailFile.uuid)" *ngIf="digitalSignature.VGCA">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                                                                <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                                                                <ng-template #elseBlock>Ký số Token</ng-template>
                                                                            </button>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openViettelCaPlugin(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, form, fd, detailFile, detailFile.uuid)" *ngIf="digitalSignature.ViettelCA">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span i18n="@@signViettelCA">Ký số Viettel-CA</span>
                                                                            </button>
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openViettelSim(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size, form, fd, detailFile, detailFile.uuid)" *ngIf="digitalSignature.ViettelSim">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span i18n="@@signViettelSim">Ký số sim Viettel</span>
                                                                            </button>
                                                                            <!-- <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'" (click)="openNEAC(f, form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.uuid, 5)" *ngIf="digitalSignature.NEAC">
                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                                                            </button> -->
                                                                            <button mat-menu-item [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'menuAction-hcm' : 'menuAction'">
                                                                                <view-sign-history [fileId]="detailFile.id">
                                                                                    <mat-icon>refresh</mat-icon>
                                                                                    <span>Xem lịch sử ký số</span>
                                                                                </view-sign-history>
                                                                            </button>
                                                                        </mat-menu>
                                                                    </div>
                                                                    <div fxLayout="row" fxLayoutAlign="space-between" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'uploadBtnNew' : 'uploadBtn'" fxFlex='grow' *ngIf="displayScanFilePadsvc">
                                                                        <button mat-button type="button" class="btn_upload"
                                                                                (click)="getInfo(form, fd.type, form.proDef); scanToPdfWithThumbnails()">
                                                                            <mat-icon>scanner</mat-icon>
                                                                            <span class="text">Scan tệp tin</span>
                                                                        </button>
                                                                    </div>
                                                                    <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'uploadBtnNew' : 'uploadBtn'" fxFlex='grow' #tooltip="matTooltip" matTooltip="Bạn có thể chọn tệp tin phù hợp với thành phần hồ sơ hoặc có thể chọn Điền biểu mẫu điện tử và ký số để thực hiện điền biểu mẫu, sau khi ký số hệ thống tự động đính kèm tệp tin vào thành phần hồ sơ.">
                                                                        <button mat-button class="btn_upload" style="width: 100%;">
                                                                            <mat-icon>attachment</mat-icon>
                                                                            <span>Chọn tệp tin</span>
                                                                        </button>
                                                                        <div *ngIf="env?.enableFileFormat === true && form?.isPicture === true ">
                                                                            <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="fileFormat" value="{{blankVal}}">
                                                                        </div>
                                                                        <div *ngIf="env?.enableFileFormat !== true || form?.isPicture !== true">
                                                                            <input type="file" multiple (change)="onSelectFile($event, form, fd.type)" [accept]="listAcceptExt" value="{{blankVal}}">
                                                                        </div>
                                                                        <div class="text-center" *ngIf="showMaxFileSize">Kích thước tối đa của tệp tin {{maxFileSize}} MB</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                    </div>
                                                    </ng-container>
                                                </mat-radio-group>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="maugiayto">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg" style="justify-content: center;">Mẫu giấy tờ</mat-header-cell>
                                            <mat-cell *matCellDef="let row" data-label="" >
                                                <mat-radio-group formControlName="rdo_File">
                                                    <div fxLayout="column"  class="divFormFile">
                                                        <div *ngIf="row.file != null && row.file.length >= 1;">
                                                            <div class="setfileForm" *ngIf="row.file != null && row.file.length > 1">
                                                                <mat-form-field appearance="fill" class="selectFileTemplate setfileForm" fxFlex="93"
                                                                                [style.z-index]="row.file.length > 0 ? 1 : -1">
                                                                    <mat-select placeholder="Xem mẫu đơn">
                                                                        <mat-option *ngFor="let file of row.file" value="{{ file.id }}"
                                                                                    (click)="downloadFile(file.id, file.filename)">
                                                                            {{ file.filename }}
                                                                        </mat-option>
                                                                    </mat-select>
                                                                </mat-form-field>
                                                            </div>
                                                            <div class="setfileForm" *ngIf="row.file != null && row.file.length == 1">
                                                                <div class="file" *ngFor="let filea of row.file" (click)="downloadFile(filea.id, filea.filename)" style="cursor:pointer; display: flex; align-items: center;" i18n-tooltip #tooltip="matTooltip" matTooltip="Tải xuống tệp tin">
                                                                    <img style="height: 30px; width: 30px;" src="{{getIconFilename(filea)}}" alt="icon file"/>
                                                                    <div class="name">{{ filea.filename }}</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div *ngIf="row.fileLink != null && row.fileLink != ''" class="linkfileForm" fxLayout="row">
                                                            <a href="{{row.fileLink}}" target="_blank">{{row?.fileLinkName ? row?.fileLinkName : 'Link file mẫu'}}</a>
                                                        </div>
                                                    </div>
                                                </mat-radio-group>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="action">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg"></mat-header-cell>
                                            <mat-cell *matCellDef="let row" data-label="">
                                                <!-- Nút kích hoạt expand -->
                                                <button *ngIf="row.checkEform == 1" mat-button style="background-color: #ce7a58; color: white;" (click)="expandRow(row)"><mat-icon>remove_red_eye</mat-icon></button>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="expandedDetail">
                                            <mat-header-cell *matHeaderCellDef class="layout-header-bdg"></mat-header-cell>
                                            <mat-cell mat-cell *matCellDef="let row; let form; let i = dataIndex" [attr.colspan]="columnsToDisplay.length" style="display: flex; justify-content: center; margin-top: 20px;">
                                                <div class="example-element-detail" [@detailExpand]="row == expandedElement ? 'expanded' : 'collapsed'" style="text-align: center;">
                                                <div class="thoaiisolate ">
                                                    <formio #formEFormComp 
                                                    [form]="applicantEForm.component" 
                                                    [submission]="applicantEForm.data" 
                                                    [renderOptions]="applicantEForm.renderOptions" 
                                                    [readOnly]="isLoadingEFormExportHGI" 
                                                    [viewOnly]="isLoadingEFormExportHGI" 
                                                    *ngIf="applicantEForm.id != undefined && applicantEForm.id != ''"
                                                    (change)="onChangeFormExportHGI($event)"></formio>
                                                </div><br />
                                                <div style="width: 100%;"> 
                                                <button *ngIf="digitalSignature.NEAC" (click)="openNEACExportHGI(i)" mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" [disabled]="isLoadingEFormExportHGI" style="background-color: #ce7a58; color: white; margin-right: 5px; margin-bottom: 5px;">
                                                    <mat-icon class="mainColor">verified</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Ký số công cộng eSign</span>
                                                </button>
                                                <button *ngIf="digitalSignature.VNPTCA" [matMenuTriggerFor]="menu"
                                                    mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" [disabled]="isLoadingEFormExportHGI" style="background-color: #ce7a58; color: white; margin-bottom: 5px; margin-right: 5px;">
                                                    <mat-icon class="mainColor">usb</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Ký số USB</span>
                                                </button>
                                                <mat-menu #menu="matMenu">
                                                            <a mat-menu-item (click)="openVnptCaPluginExportHGI(i)" style="width: 190px;">
                                                                <mat-icon style="color: #ce7a58;">draw</mat-icon>
                                                                <span style="font-weight: 500;">Ký số</span>
                                                            </a>
                                                            <a mat-menu-item href="{{urlDownloadVNPTCAPlugIn}}" style="width: 190px;" target="_blank">
                                                                <mat-icon style="color: #ce7a58;">download</mat-icon>
                                                                <span style="font-weight: 500;">Tải Plugin</span>
                                                            </a>
                                                </mat-menu>       
                                                <button mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" (click)="changeFileExportHGI('doc',i)" [disabled]="isLoading" style="background-color: #ce7a58; color: white; margin-right: 5px; margin-bottom: 5px;" >
                                                    <mat-icon class="mainColor">insert_drive_file</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Xuất file Word</span>
                                                </button>
                                                <button *ngIf="digitalSignature.VNPTSim" (click)="openPdfDigitalSignatureHGI(form?.form?.id,i, 1)" 
                                                mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" [disabled]="isLoadingEFormExportHGI" style="background-color: #ce7a58; color: white; margin-right: 5px; margin-bottom: 5px;">
                                                    <mat-icon class="mainColor">verified</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Ký số sim VNPT</span>
                                                </button>
                                                <button *ngIf="digitalSignature.SmartCA" (click)="openPdfDigitalSignatureHGI(form?.form?.id,i)" 
                                                mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" [disabled]="isLoadingEFormExportHGI" style="background-color: #ce7a58; color: white; margin-right: 5px; margin-bottom: 5px;">
                                                    <mat-icon class="mainColor">verified</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Ký số Smart CA</span>
                                                </button>
                                                <button *ngIf="digitalSignature.VGCA" (click)="openVGCAPluginExportHGI(form?.form?.id,i)" 
                                                mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" [disabled]="isLoadingEFormExportHGI" style="background-color: #ce7a58; color: white; margin-right: 5px; margin-bottom: 5px;">
                                                    <mat-icon class="mainColor">verified</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Ký số Ban Cơ yếu</span>
                                                </button>
                                                <button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" mat-flat-button style="background-color: #ce7a58; color: white; margin-right: 5px; margin-bottom: 5px;" (click)="changeFormEFormComp(i,1)" [disabled]="isLoading">
                                                    <mat-spinner diameter="25" *ngIf="isLoading === true"></mat-spinner>
                                                    <mat-icon class="mainColor">save</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Lưu lại</span>
                                                </button>
                                                <button mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" [disabled]="isLoadingEFormExportHGI" (click)="expandRow(row)" style="background-color: #ce7a58; color: white; margin-right: 5px; margin-bottom: 5px;">
                                                    <mat-icon class="mainColor">close</mat-icon>&nbsp;
                                                    <span style="vertical-align: -webkit-baseline-middle;">Đóng</span>
                                                </button>
                                                
                                                </div>
                                                </div>
                                            </mat-cell>
                                        </ng-container>
                                        <mat-header-row *matHeaderRowDef="columnsToDisplay"></mat-header-row>
                                        <mat-row *matRowDef="let element; columns: columnsToDisplay;" class="example-element-row"
                                            [class.example-expanded-row]="expandedElement === element" style="padding: 10px; width: 100%; border-top: 1px dotted #ce7a58; background-color: white;">
                                        </mat-row>
                                        <mat-row *matRowDef="let row; columns: ['expandedDetail']" class="example-detail-row" style="width: 100%; background-color: whitesmoke;" [style.display]="isRowExpanded(row) ? 'table-row' : 'none'"></mat-row>
                                </table>
                                </div>
                            </div>
                            <!--end-->
                        </form>
                    </div>
                    <br>
                    <div class="ctrl" style="display: flex; justify-content: center;">
                        <get-form-origin-item *ngIf="storage468?.getType == 'storage'"
                            (afterChoose)="afterChoose($event)"
                            [procedureId]="this.procedureId"
                            [fullname]="this.applicantEForm?.data?.data[fullname]"
                            [identityNumber]="this.applicantEForm?.data?.data[identityNumberKey]"
                            [taxcode]="this.applicantEForm?.data?.data[taxCodeKey]">
                        </get-form-origin-item>
                        <button mat-stroked-button (click)="goBack(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" i18n>Quay lại</button>
                        <button mat-flat-button (click)="confirmApplyS2(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Tiếp tục</button>
                    </div>
                </mat-step>
                <mat-step *ngIf="!isFeePaymentTemplate && !changeStepApplyDossier" [label]="labelStep3">
                    <ng-container *ngIf="bussinessType">
                        <div class="s_head">
                            <span i18n>Tìm kiếm thông tin Doanh nghiệp theo Mã số Giấy phép đăng ký Kinh doanh</span>
                        </div>
                        <br fxShow="true" fxHide.gt-sm>
                        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="width: 100%;" class="formFieldOutline">
                            <mat-form-field class="example-form-field" appearance="outline" style="margin-top: 10px;">
                                <mat-label i18n>Mã số doanh nghiệp</mat-label>
                                <input matInput type="text" [(ngModel)]="value">
                                <button mat-button *ngIf="value" matSuffix mat-icon-button aria-label="Clear" (click)="value=''">
                                    <mat-icon>close</mat-icon>
                                </button>
                            </mat-form-field>
                            <button mat-stroked-button (click)="searchBussiness()" class="btn_search" i18n>Tìm kiếm</button>
                        </div>
                        <div fxLayout="row" *ngIf="listBusinessRegistration.length > 0">
                            <ng-container>
                                <div class="user_info" fxLayout="row wrap" fxLayout.xs="column" fxLayout.sm="row wrap">
                                    <div class="title" fxFlex="grow" i18n>Người nộp hồ sơ</div>
                                    <div fxFlex.gt-sm="100" fxFlex="100" class="item">
                                        <div i18n>Tên doanh nghiệp:</div>
                                        <div>{{ listBusinessRegistration[0].name }}</div>
                                    </div>
                                    <div class="item" fxFlex="grow">
                                        <div i18n>Địa chỉ:</div>
                                        <div>{{ listBusinessRegistration[0].address }}
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                    </ng-container>

                    <div class="s_head">
                        <span i18n="@@labelHTNKQ">Hình thức nhận kết quả(*)</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <form [formGroup]="receiveForm" class="receiveForm">
                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="dossierReceivingKind != (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                            <mat-form-field [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''" *ngIf="isRestrictGetResultsAccordingToTicket == false" appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' >
                                <mat-label  i18n >Hình thức nhận kết quả</mat-label>
                                <mat-select formControlName="receivingKind" (selectionChange)="receivingKindChange($event)" required>
                                    <mat-option *ngFor="let kind of listDossierReceivingKind" [value]="kind.id">
                                        {{kind.name}}
                                        <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            
                            <mat-form-field [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''" *ngIf="isRestrictGetResultsAccordingToTicket == true"  appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' >
                                <mat-label i18n >Hình thức nhận kết quả</mat-label>
                                <mat-select formControlName="receivingKind" [disabled]="isRestrictGetResultsAccordingToTicket">
                                    <mat-option [value]="itemRestrictInDossierReceivingKind.id">
                                        {{itemRestrictInDossierReceivingKind.name}}
                                        <span *ngIf="itemRestrictInDossierReceivingKind.name == undefined || itemRestrictInDossierReceivingKind.name == null || itemRestrictInDossierReceivingKind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>

                        <!-- START ------ ViettelPost -->
                        <!-- END ------ ViettelPost -->

                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="start" *ngIf="dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                            <mat-form-field [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''" *ngIf="isRestrictGetResultsAccordingToTicket == false" appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' >
                                <mat-label i18n >Hình thức nhận kết quả</mat-label>
                                <mat-select formControlName="receivingKind" (selectionChange)="receivingKindChange($event)" required>
                                    <mat-option *ngFor="let kind of listDossierReceivingKind" [value]="kind.id">
                                        {{kind.name}}
                                        <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field *ngIf="isRestrictGetResultsAccordingToTicket == true" appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' >
                                <mat-label i18n >Hình thức nhận kết quả</mat-label>
                                <mat-select formControlName="receivingKind" [disabled]="isRestrictGetResultsAccordingToTicket" >
                                    <mat-option [value]="itemRestrictInDossierReceivingKind.id">
                                        {{itemRestrictInDossierReceivingKind.name}}
                                        <span *ngIf="itemRestrictInDossierReceivingKind.name == undefined || itemRestrictInDossierReceivingKind.name == null || itemRestrictInDossierReceivingKind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            
                            <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'" fxLayout="column" fxFlex.gt-sm="16.25" fxFlex.gt-xs="49.5" fxFlexOffset="10px" fxFlex='grow' *ngIf="env?.vnpost?.config === '1' ">
                                <mat-checkbox formControlName="rcSend"
                                    (change)="guiVNPost($event)"
                                    matTooltipClass="custom-tooltip"
                                    matTooltip="Bưu cục đến địa chỉ của người dân để nhận hồ sơ đem nộp"
                                    matTooltipPosition="right"
                                    *ngIf="hideRegistrationAtHomeButton == false">
                                    Đăng ký nộp hồ sơ tại nhà
                                </mat-checkbox>

                                <mat-checkbox formControlName="rcReceive"
                                    (change)="receiveResultAtHome($event)"
                                    matTooltipClass="custom-tooltip"
                                    matTooltip="Bưu cục gửi kết quả đến địa chỉ của người dân"
                                    matTooltipPosition="right"                                    
                                    >
                                    Đăng ký nhận kết quả tại nhà
                                </mat-checkbox>
                            </div>

                            <div class="agency" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''" *ngIf="sendVNPostChecked == true && agencyInfo[0]?.name !== undefined && agencyInfo[0]?.name.length > 0 && displayAddressAgency.isDisplayAddress == true ">
                                <span *ngFor="let agency of agencyInfo[0].name">
                                    <span *ngIf="agency.languageId == selectedLangId" class="agencyName">
                                        <span>Địa chỉ cơ quan UBND {{agency.name}}: {{agencyInfo[0]?.address?.address}}</span>
                                    </span>
                                </span>
                            </div>
                        </div>

                        <div class="nhanKqQ6" *ngIf="customNhanKqQ6">
                            Nhận trực tiếp tại Hệ thống Tiếp nhận và Trả hồ sơ tự động 24/7
                            <span>chỉ áp dụng đối với các thủ tục hành chính thuộc thẩm quyền giải quyết của Uỷ ban nhân dân Quận 6</span>
                        </div>

                        <div  fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-agency' : ''" fxLayoutAlign="space-between" *ngIf="dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress) && hideInformationEntriesVNPOST ==false">
                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                <mat-label >Tên</mat-label>
                                <input type="text" matInput formControlName="rcName" maxlength="500" *ngIf="showinforhgi" [(ngModel)]="userInfo.fullname"> 
                                <input type="text" matInput formControlName="rcName" maxlength="500" *ngIf="!showinforhgi"> 
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                <mat-label >Số điện thoại</mat-label>
                                <input type="text" matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter" *ngIf="showinforhgi" [(ngModel)]="formdata.declarationForm.phone"> 
                                <input type="text" matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter" *ngIf="!showinforhgi"> 
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                <mat-label >Email</mat-label>
                                <input type="email" matInput formControlName="rcEmail" maxlength="500" *ngIf="showinforhgi" [(ngModel)]="userInfo.email[0].value"> 
                                <input type="email" matInput formControlName="rcEmail" maxlength="500" *ngIf="!showinforhgi"> 
                            </mat-form-field>

                            <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                <div class= "groupbox_header" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''"fxFlex="100">
                                    <span *ngIf="sendVNPostChecked">
                                        Địa chỉ bưu cục đến nhận hồ sơ của người dân
                                    </span>
                                    <span *ngIf="sendVNPostChecked == false && formReceiveResultAtHome == true">
                                        Địa chỉ bưu cục gửi kết quả đến người dân
                                    </span>
                                    <span *ngIf="sendVNPostChecked == false && formReceiveResultAtHome == false">

                                    </span>
                                </div>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label i18n>Tỉnh/TP</mat-label>
                                    <mat-select formControlName="rcProvince" (selectionChange)="rcProvinceChange($event)" required>
                                        <mat-option *ngFor='let provinceOpt of listProvinceForRC;' value="{{provinceOpt.id}}">
                                            {{provinceOpt.name}}
                                            <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>

                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label>Phường/xã</mat-label>
                                    <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange($event)" [disabled]="this.receiveForm.get('rcProvince').value == ''">
                                        <mat-option *ngFor='let districtOpt of listDistrictForRC;' value="{{districtOpt.id}}">
                                            {{districtOpt.name}}
                                            <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>

                                <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label i18n>Phường/xã</mat-label>
                                    <mat-select required formControlName="rcVillage" (selectionChange)="rcVillageChange($event)" [disabled]="this.receiveForm.get('rcDistrict').value == ''">
                                        <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                                            {{wardtOpt.name}}
                                            <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field> -->

                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                    <input type="text" matInput formControlName="customAddress" maxlength="500">
                                    <!-- quocpa 07/05/2023:IGATESUPP-46164 -->
                                    <mat-error *ngIf="enableValidDetailedAddress && !receiveForm.valid">
                                        Vui lòng nhập Địa chỉ chi tiết
                                      </mat-error>
                                      <!-- end quocpa 07/05/2023:IGATESUPP-46164 -->
                                </mat-form-field>

                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="env?.vnpost?.config !== '1' || env?.vnpost === undefined || env?.vnpost === null">
                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                    <input type="text" matInput formControlName="customAddress" maxlength="500">
                                </mat-form-field>
                            </div>

                            <ng-container *ngIf="formReceiveResultAtHome && sendVNPostChecked && this.env?.vnpost?.vnpostConfigV2 == 1">
                                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                    <div class= "groupbox_header" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''" fxFlex="100">
                                        <span>Địa chỉ bưu cục gửi kết quả đến người dân</span>
                                    </div>
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Tỉnh/TP nhận kết quả</mat-label>
                                        <mat-select formControlName="rcProvinceR" (selectionChange)="rcProvinceChangeR()" required>
                                            <mat-option *ngFor='let provinceOpt of listProvinceForRCR;' value="{{provinceOpt.id}}">
                                                {{provinceOpt.name}}
                                                <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Phường/xã nhận kết quả</mat-label>
                                        <mat-select required formControlName="rcDistrictR" (selectionChange)="rcDistrictChangeR()" [disabled]="this.receiveForm.get('rcProvinceR').value == ''">
                                            <mat-option *ngFor='let districtOpt of listDistrictForRCR;' value="{{districtOpt.id}}">
                                                {{districtOpt.name}}
                                                <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                    <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Phường/xã nhận kết quả</mat-label>
                                        <mat-select formControlName="rcVillageR" [disabled]="this.receiveForm.get('rcDistrictR').value == ''">
                                            <mat-option *ngFor='let wardtOpt of listVillageForRCR;' value="{{wardtOpt.id}}">
                                                {{wardtOpt.name}}
                                                <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field> -->

                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                        <mat-label >Địa chỉ chi tiết nơi nhận kết quả</mat-label>
                                        <input type="text" matInput formControlName="customAddressR" maxlength="500">
                                        <!-- quocpa 07/05/2023:IGATESUPP-46164 -->
                                        <mat-error *ngIf="enableValidDetailedAddress && !receiveForm.valid">
                                            Vui lòng nhập Địa chỉ chi tiết nơi nhận kết quả
                                          </mat-error>
                                          <!-- end quocpa 07/05/2023:IGATESUPP-46164 -->
                                    </mat-form-field>
                                </div>
                            </ng-container>

                        </div>

                        <ng-container *ngIf="this.env?.OS_QBH?.isQBH == true && this.env?.OS_QBH?.isGetNotiForm == true">
                          <div class="s_head">
                              <span>Đăng ký nhận thông báo về việc giải quyết hồ sơ</span>
                          </div>
                          <br fxShow="true" fxHide.gt-sm>
                          <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'" fxLayout="column" fxFlex.gt-sm="16.25" fxFlex.gt-xs="49.5" fxFlexOffset="10px" fxFlex='grow'>
                              <mat-checkbox formControlName="rcSendSMS">
                                  Đăng ký nhận thông báo qua SMS
                              </mat-checkbox>

                              <mat-checkbox formControlName="rcReceiveZalo">
                                  Đăng ký nhận thông báo qua Zalo
                              </mat-checkbox>
                              <mat-checkbox formControlName="rcReceiveGmail">
                                  Đăng ký nhận thông báo qua Gmail
                              </mat-checkbox>
                          </div>
                        </ng-container>
                    </form>

                    <div class="s_head" *ngIf="enableRemoveVnpostFeeToAnotherTable == 0 && !this.displayDossierFee == true && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0">
                        <span i18n>Thông tin phí, lệ phí</span><span class="note" *ngIf="showText && dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">{{textFee}}</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div *ngIf="(haveFeeCheck == false && enableRemoveVnpostFeeToAnotherTable == 0) && !this.displayDossierFee == true && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0" class="feeZero">
                        <span i18n>Không có thông tin phí, lệ phí</span>
                    </div>
                    <div class="procedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="(this.haveFeeCheck == true && enableRemoveVnpostFeeToAnotherTable == 0) && !this.displayDossierFee == true && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0">

                      <div *ngIf="(this.hideTable && !(this.descript?.includes('miễn phí')))">
                        <span>Thanh toán trực tuyến sau thẩm định hồ sơ.</span>
                        <ul>
                          <li *ngFor="let line of this.descriptFormat"> {{ line }}</li>
                        </ul>
                      </div>

                      <div *ngIf="(this.hideTable && (this.descript?.includes('miễn phí')))">
                        <span>Miễn phí.</span>
                      </div>

                      <mat-table *ngIf="!this.hideTable" [dataSource]="feeDataSource" fxFlex='grow' class="tblFee">
                            <ng-container matColumnDef="procostType">
                                <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                                    {{row.typeName}}
                                    <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="!allowFeeAmountInput && !enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="row.type.quantityEditable != 1">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="!allowFeeAmountInput && enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="row.type.quantityEditable != 1">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="allowFeeAmountInput && !enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="!checkQuantityEditable(row.type.id)">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="checkQuantityEditable(row.type.id)">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="allowFeeAmountInput && enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="!checkQuantityEditable(row.type.id)">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="checkQuantityEditable(row.type.id)">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="cost" class="fee-column" *ngIf="!changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                    <mat-select value="0" (selectionChange)="changeCount(row.stt, $event)" class="select-fee">
                                        <mat-option *ngFor='let cost of row.costCase; let j = index;'value="{{j}}"
                                            title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                            [disabled]="env?.OS_HCM?.priceSelectDisabled == 1 ? true : false">
                                            {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                                        </mat-option>
                                    </mat-select>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="cost" class="fee-column" *ngIf="changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                    <mat-select value="0" (selectionChange)="changeCount(row.stt, $event)" class="select-fee">
                                        <mat-option *ngFor='let cost of row.costCase; let j = index;'value="{{j}}"
                                            title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                            [disabled]="env?.OS_HCM?.priceSelectDisabled == 1 ? true : false">
                                            <span *ngIf = "cost.cost == '0'">Chưa thẩm định </span>
                                            <span *ngIf = "cost.cost !== '0'">{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>



                            <ng-container matColumnDef="amount" *ngIf="!changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                    {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
                                <mat-footer-cell *matFooterCellDef class="totalCell" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''">{{totalCost}}</mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="amount" *ngIf="changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                    <span *ngIf = "row.cost == '0'">Chưa thẩm định </span>
                                    <span *ngIf = "row.cost !== '0'">{{row.quantity*row.cost | number }} {{row.monetaryUnit}} </span>
                                     </mat-cell>
                                <mat-footer-cell *matFooterCellDef class="totalCell">
                                    <span *ngIf = "totalCost == '0'">Chưa thẩm định </span>
                                    <span *ngIf = "totalCost !== '0'">{{totalCost}} </span></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="compulsory">
                                <mat-header-cell *matHeaderCellDef i18n>Bắt buộc</mat-header-cell>
                                <ng-container *ngIf="changeLanguageForSCT; then SCTAgency else anotherAgency;"></ng-container>
                                    <ng-template #SCTAgency>
                                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                            <span *ngIf="row?.required !== 1">{{OS_HCM_SCT?.contentTTS}}</span>
                                            <span *ngIf="row?.required === 1">Có</span>
                                        </mat-cell>
                                    </ng-template>
                                    <ng-template #anotherAgency>
                                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                            <span *ngIf="row?.required !== 1">Không</span>
                                            <span *ngIf="row?.required === 1">Có</span>
                                        </mat-cell>
                                    </ng-template>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="description">
                                <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                            <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                            <div *ngIf="hideTotal == false">
                                <mat-footer-row *matFooterRowDef="feeDisplayedColumns"></mat-footer-row>
                            </div>
                        </mat-table>
                    </div>
                    <!-- phucnh.it2-IGATESUPP-31658: Tach vnpostFee -->
                    <!-- table vnpost Fee -->
                    <div *ngIf="(dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)) && !this.displayDossierFee == true && this.hideDossierFeeCTO == 0">
                        <div class="s_head" *ngIf="this.enableRemoveVnpostFeeToAnotherTable == 1">
                            <span i18n="@@vnPostDescriptionTitleH1">Thông tin phí thu hồ sơ/trả kết quả tại nhà</span><br fxShow="true" fxHide.gt-sm>
                        </div>
                        <div class="s_head" *ngIf="this.enableRemoveVnpostFeeToAnotherTable == 1">
                            <span class='payment-status-vnpost' i18n="@@vnPostDescriptionTitleH2">(Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả)</span>
                        </div>
                        <br fxShow="true" fxHide.gt-sm>
                        <div *ngIf="this.isEnableUseVNPost && this.enableRemoveVnpostFeeToAnotherTable == 1 && this.isShowCostVNPost" class="feeZero">
                            <span >Phí trả kết quả hồ sơ tại nhà: </span> {{this.costVNPost}} (VNĐ)
                        </div>
                        <div *ngIf="haveFeeVnpostCheck == false && this.enableRemoveVnpostFeeToAnotherTable == 1 && !this.isShowCostVNPost" class="feeZero">
                            <span i18n>Không có thông tin phí, lệ phí</span>
                        </div>
                        <div class="procedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="this.haveFeeVnpostCheck == true && this.enableRemoveVnpostFeeToAnotherTable == 1">
                            <mat-table [dataSource]="feeVnpostDataSource" fxFlex='grow' class="tblFee">
                                <ng-container matColumnDef="procostType">
                                    <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                                        {{row.typeName}}
                                        <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                                </ng-container>

                                <ng-container *ngIf="!allowFeeAmountInput" matColumnDef="quantity">
                                    <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                        <div *ngIf="row.type.quantityEditable != 1">
                                            {{row.quantity}}
                                        </div>
                                        <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                            <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                        </mat-form-field>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container *ngIf="allowFeeAmountInput" matColumnDef="quantity">
                                    <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                        <div *ngIf="!checkQuantityEditable(row.type.id)">
                                            {{row.quantity}}
                                        </div>
                                        <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="checkQuantityEditable(row.type.id)">
                                            <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                        </mat-form-field>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="cost" class="fee-column">
                                    <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                        <mat-select value="0" (selectionChange)="changeCountVnpostFee(row.stt, $event)" class="select-fee">
                                            <mat-option *ngFor='let cost of row.costCase; let j = index;'value="{{j}}"
                                                title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                                [disabled]="env?.OS_HCM?.priceSelectDisabled == 1 ? true : false">
                                                {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                                            </mat-option>
                                        </mat-select>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>


                                <ng-container matColumnDef="amount">
                                    <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                        {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
                                    <mat-footer-cell *matFooterCellDef class="totalCell" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''">{{totalFeeVnpostCost}}</mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="compulsory">
                                    <mat-header-cell *matHeaderCellDef i18n>Bắt buộc</mat-header-cell>
                                    <ng-container *ngIf="changeLanguageForSCT; then SCTAgency else anotherAgency;"></ng-container>
                                        <ng-template #SCTAgency>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                                <span *ngIf="row?.required !== 1">{{OS_HCM_SCT?.contentTTS}}</span>
                                                <span *ngIf="row?.required === 1">Có</span>
                                            </mat-cell>
                                        </ng-template>
                                        <ng-template #anotherAgency>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                                <span *ngIf="row?.required !== 1">Không</span>
                                                <span *ngIf="row?.required === 1">Có</span>
                                            </mat-cell>
                                        </ng-template>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="description">
                                    <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef class="payment-status-vnpost" i18n="@@vnPostDescriptionTotal">(Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả)</mat-footer-cell>
                                </ng-container>

                                <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                                <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                                <div *ngIf="hideTotal == false">
                                    <mat-footer-row *matFooterRowDef="feeDisplayedColumns"></mat-footer-row>
                                </div>
                            </mat-table>
                        </div>
                    </div>
                    <!-- table phi/le phi hồ sơ-->
                    <div class="s_head" *ngIf="this.enableRemoveVnpostFeeToAnotherTable == 1 && !this.displayDossierFee == true  && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0">
                        <span i18n>Thông tin phí, lệ phí</span><br />
                    </div>
                    <div class="s_head" *ngIf="(this.enableRemoveVnpostFeeToAnotherTable == 1 && dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)) && !this.displayDossierFee == true && this.hideDossierFeeCTO == 0">
                        <span class='payment-status-vnpost' i18n="@@dossierFeeDescriptionTitle">(Hiện tại hệ thống chỉ áp dụng thu phí, lệ phí hồ sơ. *Lưu ý: Đối với phí dịch vụ bưu chính (nếu có) người dân vui lòng thanh toán trực tiếp cho nhân viên bưu chính khi nhận kết quả tại nhà)</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div *ngIf="(this.haveFeeDossierCheck == false && this.enableRemoveVnpostFeeToAnotherTable == 1) && !this.displayDossierFee == true && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0" class="feeZero">
                        <span i18n>Không có thông tin phí, lệ phí</span>
                    </div>
                    <div class="procedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="(this.haveFeeDossierCheck == true && this.enableRemoveVnpostFeeToAnotherTable == 1) && !this.displayDossierFee == true && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0">

                        <mat-table [dataSource]="feeDossierDataSource" fxFlex='grow' class="tblFee">
                            <ng-container matColumnDef="procostType">
                                <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                    {{row.typeName}}&nbsp;
                                    <span *ngIf="this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1" i18n="@@hasManyFee">(Có nhiều mức phí)</span>
                                    <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="!allowFeeAmountInput && enableMappingNumberCopiesFees == false && !enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                    <div *ngIf="row.type.quantityEditable != 1">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>
                            <!-- phucnh.it2-IGATESUPP-38054 -->
                            <ng-container *ngIf="!allowFeeAmountInput && enableMappingNumberCopiesFees == true && !enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="row.type.quantityEditable != 1 && (typeFee.includes(row.type?.id) && this.tempNumberCopyByTypeFee[row?.id] != undefined)" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                        {{this.tempNumberCopyByTypeFee[row?.id]}}
                                    </div>
                                    <div *ngIf="row.type.quantityEditable != 1 && (!typeFee.includes(row.type?.id) || this.tempNumberCopyByTypeFee[row?.id] == undefined)" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>
                            <!-- end phucnh.it2-IGATESUPP-38054 -->

                            <ng-container *ngIf="!allowFeeAmountInput && enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                              <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                              <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                  <div *ngIf="row.type.quantityEditable != 1" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                      {{row.quantity}}
                                  </div>
                                  <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                      <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                  </mat-form-field>
                              </mat-cell>
                              <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="allowFeeAmountInput" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="!checkQuantityEditable(row.type.id)" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="checkQuantityEditable(row.type.id)">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="cost" class="fee-column">
                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                    <mat-select value="0" (selectionChange)="changeCountDossierFee(row.stt, $event)" class="select-fee" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                        <mat-option *ngFor='let cost of row.costCase; let j = index;'value="{{j}}"
                                            title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                            [disabled]="env?.OS_HCM?.priceSelectDisabled == 1 ? true : false">
                                            {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                                        </mat-option>
                                    </mat-select>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>


                            <ng-container *ngIf="enableMappingNumberCopiesFees == false" matColumnDef="amount">
                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                    {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
                                <mat-footer-cell *matFooterCellDef class="totalCell" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''">{{totalFeeDossierCost}}</mat-footer-cell>
                            </ng-container>

                            <!-- phucnh.it2-IGATESUPP-38054 -->
                            <ng-container *ngIf="enableMappingNumberCopiesFees == true" matColumnDef="amount">
                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                <mat-cell  *matCellDef="let row" i18n-data-label data-label="Thành tiền" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}">
                                    <span *ngIf="typeFee.includes(row.type?.id)"> {{this.tempNumberCopyByTypeFee[row?.id]*row.cost | number }} {{row.monetaryUnit}} </span>
                                    <span  *ngIf="!typeFee.includes(row.type?.id)"> {{row.quantity*row.cost | number }} {{row.monetaryUnit}}  </span>
                                    <!-- {{ (row.type.id == typeFee ? this.soLuongBanSaoYeuCauCap*row.cost : row.quantity*row.cost) | number }} -->
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef class="totalCell" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''">{{totalFeeDossierCost}}</mat-footer-cell>
                            </ng-container>
                            <!-- end phucnh.it2-IGATESUPP-38054 -->

                            <ng-container matColumnDef="compulsory">
                                <mat-header-cell *matHeaderCellDef i18n>Bắt buộc</mat-header-cell>
                                <ng-container *ngIf="changeLanguageForSCT; then SCTAgency else anotherAgency;"></ng-container>
                                    <ng-template #SCTAgency>
                                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                            <span *ngIf="row?.required !== 1">{{OS_HCM_SCT?.contentTTS}}</span>
                                            <span *ngIf="row?.required === 1">Có</span>
                                        </mat-cell>
                                    </ng-template>
                                    <ng-template #anotherAgency>
                                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                            <span *ngIf="row?.required !== 1">Không</span>
                                            <span *ngIf="row?.required === 1">Có</span>
                                        </mat-cell>
                                    </ng-template>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="description">
                                <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" [ngClass]="{'procost-highlight': this.procostConfig.displayHighlightMultiCost == true && row.costCase.length > 1}"> {{row.description}}
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                            <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                            <div *ngIf="hideTotal == false">
                                <mat-footer-row *matFooterRowDef="feeDisplayedColumns"></mat-footer-row>
                            </div>
                        </mat-table>
                    </div>
                    <div *ngIf="(this.procostConfig.displayHighlightMultiCost == true && this.procostHasMultipleFee) && !this.displayDossierFee == true && this.hideDossierFeeCTO == 0">
                        <span class="procost-highlight" i18n="@@paymentHasManyFees">(Dòng lệ phí có nhiều mức phí có màu đỏ)</span>
                    </div>
                    <!-- BEGIN IGATESUPP-67053 -->
                    <ng-container *ngIf="this.displayHinhThucThanhToan == true"> 
                        <!-- phucnh.it2-IGATESUPP-31658: End -->
                        <div class="s_head" *ngIf="this.haveFeeCheck == true && !this.displayDossierFee == true && !hidePaymentMethod && this.hideDossierFeeCTO == 0">
                            <span i18n>Chọn hình thức thanh toán</span><span class="required">(*)</span>
                            <span class="note" *ngIf="isEnableNotePayment">Khuyến khích doanh nghiệp/cơ sở thanh toán phí trực tuyến</span>
                        </div>
                        <br fxShow="true" fxHide.gt-sm>
                        <!-- dossierReceivingKind != this.config.receiveResultsByAddress -->
                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="this.haveFeeCheck == true && !this.displayDossierFee == true && !hidePaymentMethod && this.hideDossierFeeCTO == 0">
                            <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <mat-select msInfiniteScroll [(value)]="paymentMethodId" (selectionChange)="onPaymentMethodChoose($event)" (infiniteScroll)="getListPaymentMethod()" [complete]="listPaymentMethodFull == true"  [disabled]="env?.OS_DNI?.isDisabledHinhThucThanhToan == 1 ? true : false" >
                                    <mat-option *ngFor='let paymentMethod of listPaymentMethod;' value="{{paymentMethod.id}}" >
                                        {{paymentMethod.name}}
                                        <span *ngIf="paymentMethod.name == undefined || paymentMethod.name == null || paymentMethod.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>

                            </mat-form-field>
                        </div>
                        <div class="s_head" *ngIf="(!isHideInfoPayment && !isUserRQ && !hidePaymentInfor && this.checkPaymentFlatform == true) && !this.displayDossierFee == true && this.hideDossierFeeCTO == 0" fxLayout="column">
                            <p *ngIf="!!this.accoutPayment.id"><span class="fontweight500">Cơ quan thụ hưởng:</span> {{this.accoutPayment.agencyName}}</p>
                            <p *ngIf="!this.accoutPayment.id"><span class="fontweight500">{{this.accoutPayment.name}} </span></p>
                            <p><span class="fontweight500">Tài khoản:</span> {{this.accoutPayment.beneficiaryAccount}}</p>
                            <p><span class="fontweight500">Tên tài khoản:</span> {{this.accoutPayment.beneficiaryAccountName}}</p>
                            <p><span class="fontweight500">Mã ngân hàng:</span> {{this.accoutPayment.beneficiaryBankCode}}</p>
                        </div>
                        <div class="ctrl" *ngIf="(allowSelectReceipt && allowSelectReceipt.enable && allowSelectReceiptPayment) && !this.displayDossierFee == true && this.hideDossierFeeCTO == 0">
                            <div class="s_head ">
                                <span>Đề nghị cá nhân/doanh nghiệp/cơ sở lựa chọn:</span>
                            </div>
                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row"  style="margin-bottom: 15px; margin-left: 0px;">
                                <mat-radio-group style="text-align: left;" aria-label="Select an option" formControlName="typeDate" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-require' : ''"><br>
                                        <mat-radio-button value="1" [checked]="true" (change)="onRadioChangeReceipt($event)">Xuất biên lai cho cá nhân</mat-radio-button><br><br>                              
                                        <mat-radio-button value="2" (change)="onRadioChangeReceipt($event)">Xuất biên lai cho doanh nghiệp/cơ sở</mat-radio-button>
                                </mat-radio-group>
                            </div>  
                            <!-- <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline captcha" fxLayoutAlign="space-between" style="border-bottom: 1px solid #e0e0e059;margin-bottom: 15px;">
                                <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'" appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-checkbox [checked]="checkReceiptPersion" (change)="receiptPersionChange()">
                                        <span>Xuất biên lai cho cá nhân</span>
                                    </mat-checkbox>
                                </div>
                                <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'" appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-checkbox [checked]="checkReceiptCompany" (change)="receiptCompanyChange()">
                                        <span>Xuất biên lai cho doanh nghiệp/cơ sở</span>
                                    </mat-checkbox>
                                </div>
                                                        
                                <hr/>
                            </div> -->
                        </div>
                    </ng-container>
                    <!-- END IGATESUPP-67053 -->
                    <div class="ctrl">
                        <div class="captcha">
                            <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'">
                                <mat-checkbox (change)="changeConfirmStatus($event)">
                                    <span i18n>Tôi chắc chắn rằng các thông tin khai báo trên là đúng sự thật và đồng ý chịu trách nhiệm trước pháp luật về lời khai trên.</span>
                                </mat-checkbox>
                            </div>
                            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="captchaImgage formFieldOutline" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-agency' : ''" *ngIf="this.captcharOpen == 1">
                                <form [formGroup]="captchaForm" (submit)="captchaSubmit()" id="captchaForm" fxFlex='20' fxLayout="row">
                                    <mat-form-field appearance="outline" fxFlex='grow'>
                                        <mat-label i18n>Mã xác nhận</mat-label>
                                        <input matInput formControlName="captcha" required>
                                        <mat-error *ngIf="captcha.invalid" class="error_Msg">
                                            <span i18n>Vui lòng nhập mã xác nhận</span>
                                            <div class="err">
                                                <mat-icon>priority_high</mat-icon>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                </form>
                                <div fxFlex='8' class="chCode">
                                    <div [innerHTML]="captchaImg" class="chIMG" (click)="generateCaptcha()"></div>
                                    <button mat-flat-button (click)="generateCaptcha()">
                                        <mat-icon>cached</mat-icon>
                                    </button>
                                </div>
                            </div>
                        </div>
                                                
                        <div class="feeRefund" *ngIf = "feeRefund && (sumCost > 0 || sumFeeDossierCost > 0)">
                            <div class="s_head">
                                <span >Đăng ký thông tin hoàn tiền</span>
                            </div>
                            <br fxShow="true" fxHide.gt-sm>
                            
                            <form [formGroup]="feeRefundForm" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color-feeRefund' : ''" fxLayoutAlign="space-between" style="margin-top: 16px;">
                                <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-label >Số tài khoản</mat-label>
                                    <input type="text" matInput formControlName="stk" maxlength="500" required="{{feeRefundRequired}}">
                                    <mat-error *ngIf="feeRefundRequired && feeRefundForm.controls.stk.invalid" class="error_Msg">
                                        <span>Vui lòng nhập Số tài khoản</span>
                                        <div class="err">
                                            <mat-icon>priority_high</mat-icon>
                                        </div>
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-label >Chủ tài khoản</mat-label>
                                    <input type="text" matInput formControlName="owner" maxlength="500" required="{{feeRefundRequired}}">
                                    <mat-error *ngIf="feeRefundRequired && feeRefundForm.controls.stk.invalid" class="error_Msg">
                                        <span>Vui lòng nhập Chủ tài khoản</span>
                                        <div class="err">
                                            <mat-icon>priority_high</mat-icon>
                                        </div>
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-label >Tên ngân hàng</mat-label>
                                    <input type="text" matInput formControlName="bank" maxlength="500" required="{{feeRefundRequired}}">
                                    <mat-error *ngIf="feeRefundRequired && feeRefundForm.controls.stk.invalid" class="error_Msg">
                                        <span>Vui lòng nhập Tên ngân hàng</span>
                                        <div class="err">
                                            <mat-icon>priority_high</mat-icon>
                                        </div>
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-label >Địa chỉ đơn vị thụ hưởng</mat-label>
                                    <input type="text" matInput formControlName="address" maxlength="500">
                                </mat-form-field>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-label >Chi nhánh ngân hàng</mat-label>
                                    <input type="text" matInput formControlName="branch" maxlength="500">
                                </mat-form-field>
                                <div fxFlex.gt-sm="33"></div>
                            </form>
                        </div>
                        
                        <button mat-stroked-button (click)="goBack(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" i18n>Quay lại</button>
                        <!-- <button mat-flat-button (click)="confirmPay(stepper); captchaSubmit(); sendVNPostOverLgspHcmAfterPayment()" form="captchaForm" *ngIf="!confirmPayName && isConfirm && this.haveFeeCheck == true && this.checkHcmNotSendVNPostConfig == 0 " [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Thanh toán</button> -->
                        <!-- <button mat-flat-button (click)="confirmPay(stepper); captchaSubmit(); sendVNPostOverLgspHcmAfterPayment()" form="captchaForm" *ngIf="confirmPayName && isConfirm && this.haveFeeCheck == true && this.checkHcmNotSendVNPostConfig == 0 " [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'">{{confirmPayName}}</button> -->
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit(); " form="captchaForm" *ngIf="!confirmPayName && isConfirm && this.haveFeeCheck == true && this.displayHinhThucThanhToan == true && !continuepaymenthgi" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Thanh toán</button> 
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit(); " form="captchaForm" *ngIf="!confirmPayName && isConfirm && this.haveFeeCheck == true && this.displayHinhThucThanhToan == true && continuepaymenthgi" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Tiếp tục</button> 
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit();" form="captchaForm" *ngIf="confirmPayName && isConfirm && this.haveFeeCheck == true && this.displayHinhThucThanhToan == true" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'">{{confirmPayName}}</button>
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit()" form="captchaForm" *ngIf="isConfirm && (this.haveFeeCheck == false || this.displayHinhThucThanhToan == false)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Tiếp tục</button>
                    </div>
                </mat-step>
                <mat-step *ngIf="isFeePaymentTemplate && !changeStepApplyDossier" [label]="labelStep3">
                    <ng-container *ngIf="bussinessType">
                        <div class="s_head">
                            <span i18n>Tìm kiếm thông tin Doanh nghiệp theo Mã số Giấy phép đăng ký Kinh doanh</span>
                        </div>
                        <br fxShow="true" fxHide.gt-sm>
                        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="width: 100%;" class="formFieldOutline">
                            <mat-form-field class="example-form-field" appearance="outline" style="margin-top: 10px;">
                                <mat-label i18n>Mã số doanh nghiệp</mat-label>
                                <input matInput type="text" [(ngModel)]="value">
                                <button mat-button *ngIf="value" matSuffix mat-icon-button aria-label="Clear" (click)="value=''">
                                    <mat-icon>close</mat-icon>
                                </button>
                            </mat-form-field>
                            <button mat-stroked-button (click)="searchBussiness()" class="btn_search" i18n>Tìm kiếm</button>
                        </div>
                        <div fxLayout="row" *ngIf="listBusinessRegistration.length > 0">
                            <ng-container>
                                <div class="user_info" fxLayout="row wrap" fxLayout.xs="column" fxLayout.sm="row wrap">
                                    <div class="title" fxFlex="grow" i18n>Người nộp hồ sơ</div>
                                    <div fxFlex.gt-sm="100" fxFlex="100" class="item">
                                        <div i18n>Tên doanh nghiệp:</div>
                                        <div>{{ listBusinessRegistration[0].name }}</div>
                                    </div>
                                    <div class="item" fxFlex="grow">
                                        <div i18n>Địa chỉ:</div>
                                        <div>{{ listBusinessRegistration[0].address }}
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                    </ng-container>

                    <div *ngIf="registerAtHomeBCCI" class="s_head">
                        <span>Hình thức nộp hồ sơ</span>
                    </div>
                    <div *ngIf="registerAtHomeBCCI">
                        <span>Đăng ký nộp hồ sơ tại nhà qua dịch vụ BCCI</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <form [formGroup]="receiveForm" class="receiveForm">
                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="registerAtHomeBCCI">
                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                <mat-label >Tên</mat-label>
                                <input type="text" matInput formControlName="rcName" maxlength="500">
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                <mat-label >Số điện thoại</mat-label>
                                <input type="text" matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter">
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                <mat-label >Email</mat-label>
                                <input type="email" matInput formControlName="rcEmail" maxlength="500">
                            </mat-form-field>

                            <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                <div class= "groupbox_header" fxFlex="100">
                                    <span>
                                        Địa chỉ bưu cục đến nhận hồ sơ của người dân
                                    </span>
                                </div>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label i18n>Tỉnh/TP</mat-label>
                                    <mat-select formControlName="rcProvince" (selectionChange)="rcProvinceChange()" required>
                                        <mat-option *ngFor='let provinceOpt of listProvinceForRC;' value="{{provinceOpt.id}}">
                                            {{provinceOpt.name}}
                                            <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>

                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label>Phường/xã</mat-label>
                                    <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()" [disabled]="this.receiveForm.get('rcProvince').value == ''">
                                        <mat-option *ngFor='let districtOpt of listDistrictForRC;' value="{{districtOpt.id}}">
                                            {{districtOpt.name}}
                                            <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>

                                <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label i18n>Phường/xã</mat-label>
                                    <mat-select required formControlName="rcVillage" [disabled]="this.receiveForm.get('rcDistrict').value == ''">
                                        <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                                            {{wardtOpt.name}}
                                            <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field> -->

                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                    <input type="text" matInput formControlName="customAddress" maxlength="500">
                                    <!-- quocpa 07/05/2023:IGATESUPP-46164 -->
                                    <mat-error *ngIf="enableValidDetailedAddress && !receiveForm.valid">
                                        Vui lòng nhập Địa chỉ chi tiết
                                      </mat-error>
                                      <!-- end quocpa 07/05/2023:IGATESUPP-46164 -->
                                </mat-form-field>

                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="env?.vnpost?.config !== '1' || env?.vnpost === undefined || env?.vnpost === null">
                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                    <input type="text" matInput formControlName="customAddress" maxlength="500">
                                </mat-form-field>
                            </div>
                        </div>

                        <div class="s_head">
                            <span>Hình thức nhận kết quả</span>
                        </div>
                        <br fxShow="true" fxHide.gt-sm>

                        <div>
                            <div class="row">
                                <mat-radio-button [value]="0" [checked]="selectedLevel === 0"
                                    (change)="onRadioFeePaymentChange($event)">
                                    <span *ngIf="!qbhFormOnlineFee">Nhận trực tiếp</span>
                                    <span *ngIf="qbhFormOnlineFee">
                                        <span *ngIf="agencyInfo[0]?.name !== undefined && agencyInfo[0]?.name.length > 0">
                                            <span *ngFor="let agency1 of agencyInfo[0].name">
                                                <span *ngIf="agency1.languageId == selectedLangId">
                                                    <span>Nhận trực tiếp tại Trung tâm hành chính công hoặc Bộ phận một cửa của {{agency1.name}}</span>
                                                </span>
                                            </span>
                                        </span>
                                    </span>
                                </mat-radio-button>
                            </div>
                            <div class="row">
                                <mat-radio-button [value]="1" [checked]="selectedLevel === 1"
                                    (change)="onRadioFeePaymentChange($event)">
                                    <span *ngIf="!formPostalService">Đăng ký nhận hồ sơ tại nhà qua dịch vụ BCCI</span>
                                    <span *ngIf="formPostalService">Đăng ký nhận hồ sơ tại nhà qua dịch vụ bưu chính</span>
                                    <span *ngIf="qbhFormOnlineFee && !formPostalService">(cá nhân, tổ chức thanh toán cước phí cho bưu điện khi nhận kết quả)</span>
                                    <span *ngIf="qbhFormOnlineFee && formPostalService">(cá nhân, tổ chức thanh toán cước phí khi nhận kết quả)</span>
                                </mat-radio-button>
                            </div>
                            <div *ngIf="qbhFormOnlineFee" class="row">
                                <mat-radio-button [value]="2" [checked]="selectedLevel === 2"
                                    (change)="onRadioFeePaymentChange($event)">
                                    <span>Nhận trực tuyến</span>
                                </mat-radio-button>
                            </div>
                        </div>

                        <!-- <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="start" >
                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' >
                                <mat-label i18n>Hình thức nhận kết quả</mat-label>
                                <mat-select formControlName="receivingKind" (selectionChange)="receivingKindChange($event)" required>
                                    <mat-option *ngFor="let kind of listDossierReceivingKind" [value]="kind.id">
                                        {{kind.name}}
                                        <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div> -->

                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="start" *ngIf="selectedLevel === 1 && formPostalService"  style="margin-top: 20px;">
                           <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                             <mat-label>Chọn nhà cung cấp</mat-label>
                             <mat-select formControlName="suppliers" (selectionChange)="suppliersChange($event)">
                               <mat-option required *ngFor="let supplier of listSuppliers" [value]="supplier.id">
                                 {{supplier.name}}
                                 <span *ngIf="supplier.name == undefined || supplier.name == null || supplier.name.trim() == ''">(Không tìm thấy bản dịch)</span>
                               </mat-option>
                             </mat-select>
                           </mat-form-field>
                        </div>

                        <ng-container *ngIf="viettelPostEnable && selectedLevel === 1 && selectedSuppliers === 2; then viettelPost else anotherPost;"></ng-container>
                        <!-- START ------ ViettelPost -->
                        <ng-template #viettelPost>
                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="start center" fxLayoutGap="20px" style="margin-top: 16px;">
                                <mat-checkbox formControlName="vpSend" matTooltipClass="custom-tooltip" matTooltip="Đăng ký nộp hồ sơ tại nhà" matTooltipPosition="right" (change)="onVpSendChange($event)">
                                    Đăng ký nộp hồ sơ tại nhà
                                </mat-checkbox>
                                <mat-checkbox formControlName="vpReceive" matTooltipClass="custom-tooltip" matTooltip="Đăng ký nhận kết quả tại nhà" matTooltipPosition="right" (change)="onVpReceiveChange($event)">
                                    Đăng ký nhận kết quả tại nhà
                                </mat-checkbox>
                            </div>
                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between">
                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label >Tên</mat-label>
                                    <input type="text" matInput formControlName="vpName" maxlength="150">
                                </mat-form-field>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label >Số điện thoại</mat-label>
                                    <input type="text" matInput formControlName="vpPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter">
                                </mat-form-field>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label >Email</mat-label>
                                    <input type="email" matInput formControlName="vpEmail" maxlength="150">
                                </mat-form-field>
                            </div>

                            <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between" *ngIf="viettelPostAgencyAddressEnable">
                                <div class= "groupbox_header" fxFlex="100">
                                    <span>Địa chỉ cơ quan tiếp nhận hồ sơ</span>
                                </div>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label i18n>Tỉnh/TP</mat-label>
                                    <mat-select formControlName="vpProvinceA" (selectionChange)="vpProvinceChange('1', $event)" required>
                                        <mat-option *ngFor='let provinceA of listProvinceForVP;' value="{{provinceA.id}}">
                                            {{provinceA.name}}
<!--                                            <span *ngIf="provinceA.name == undefined || provinceA.name == null || provinceA.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                    <mat-label i18n>Phường/xã</mat-label>
                                    <mat-select required formControlName="vpDistrictA" (selectionChange)="vpDistrictChange('1', $event)" [disabled]="this.receiveForm.get('vpProvinceA').value == ''">
                                        <mat-option *ngFor='let districtA of listDistrictForVPA;' value="{{districtA.id}}">
                                            {{districtA.name}}
<!--                                            <span *ngIf="districtA.name == undefined || districtA.name == null || districtA.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
<!--                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>-->
<!--                                    <mat-label i18n>Phường/xã</mat-label>-->
<!--                                    <mat-select required formControlName="vpVillageA" (selectionChange)="vpVillageChange('1', $event)" [disabled]="this.receiveForm.get('vpDistrictA').value == ''">-->
<!--                                        <mat-option *ngFor='let villageA of listVillageForVPA;' value="{{villageA.id}}">-->
<!--                                            {{villageA.name}}-->
<!--&lt;!&ndash;                                            <span *ngIf="villageA.name == undefined || villageA.name == null || villageA.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>&ndash;&gt;-->
<!--                                        </mat-option>-->
<!--                                    </mat-select>-->
<!--                                </mat-form-field>-->
                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                    <input type="text" matInput formControlName="vpAddressDetailA" maxlength="150" (keyup.enter)="onEnterFinished('1', $event)">
                                </mat-form-field>
                            </div>

                            <ng-container *ngIf="receiveForm.get('vpSend')?.value">
                                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                    <div class= "groupbox_header" fxFlex="100">
                                        <span>Địa chỉ bưu cục đến nhận hồ sơ của người dân</span>
                                    </div>
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label i18n>Tỉnh/TP</mat-label>
                                        <mat-select formControlName="vpProvince" (selectionChange)="vpProvinceChange('2', $event)" required>
                                            <mat-option *ngFor='let province of listProvinceForVP;' value="{{province.id}}">
                                                {{province.name}}
<!--                                                <span *ngIf="province.name == undefined || province.name == null || province.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label i18n>Phường/xã</mat-label>
                                        <mat-select required formControlName="vpDistrict" (selectionChange)="vpDistrictChange('2', $event)" [disabled]="this.receiveForm.get('vpProvince').value == ''">
                                            <mat-option *ngFor='let district of listDistrictForVP;' value="{{district.id}}">
                                                {{district.name}}
<!--                                                <span *ngIf="district.name == undefined || district.name == null || district.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
<!--                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>-->
<!--                                        <mat-label i18n>Phường/xã</mat-label>-->
<!--                                        <mat-select required formControlName="vpVillage" (selectionChange)="vpVillageChange('2', $event)" [disabled]="this.receiveForm.get('vpDistrict').value == ''">-->
<!--                                            <mat-option *ngFor='let village of listVillageForVP;' value="{{village.id}}">-->
<!--                                                {{village.name}}-->
<!--&lt;!&ndash;                                                <span *ngIf="village.name == undefined || village.name == null || village.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>&ndash;&gt;-->
<!--                                            </mat-option>-->
<!--                                        </mat-select>-->
<!--                                    </mat-form-field>-->
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                        <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                        <input type="text" matInput formControlName="vpAddressDetail" maxlength="150" (keyup.enter)="onEnterFinished('2', $event)">
                                    </mat-form-field>
                                </div>
                            </ng-container>

                            <ng-container *ngIf="receiveForm.get('vpReceive')?.value">
                                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                    <div class= "groupbox_header" fxFlex="100">
                                        <span>Địa chỉ bưu cục gửi kết quả đến người dân</span>
                                    </div>
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Tỉnh/TP nhận kết quả</mat-label>
                                        <mat-select formControlName="vpProvinceR" (selectionChange)="vpProvinceChange('3', $event)" required>
                                            <mat-option *ngFor='let provinceR of listProvinceForVP;' value="{{provinceR.id}}">
                                                {{provinceR.name}}
<!--                                                <span *ngIf="provinceR.name == undefined || provinceR.name == null || provinceR.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Phường/xã nhận kết quả</mat-label>
                                        <mat-select required formControlName="vpDistrictR" (selectionChange)="vpDistrictChange('3', $event)" [disabled]="this.receiveForm.get('vpProvinceR').value == ''">
                                            <mat-option *ngFor='let districtR of listDistrictForVPR;' value="{{districtR.id}}">
                                                {{districtR.name}}
<!--                                                <span *ngIf="districtR.name == undefined || districtR.name == null || districtR.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
<!--                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>-->
<!--                                        <mat-label>Phường/xã nhận kết quả</mat-label>-->
<!--                                        <mat-select formControlName="vpVillageR" (selectionChange)="vpVillageChange('3', $event)" [disabled]="this.receiveForm.get('vpDistrictR').value == ''">-->
<!--                                            <mat-option *ngFor='let villageR of listVillageForVPR;' value="{{villageR.id}}">-->
<!--                                                {{villageR.name}}-->
<!--&lt;!&ndash;                                                <span *ngIf="villageR.name == undefined || villageR.name == null || villageR.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>&ndash;&gt;-->
<!--                                            </mat-option>-->
<!--                                        </mat-select>-->
<!--                                    </mat-form-field>-->
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                        <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                        <input type="text" matInput formControlName="vpAddressDetailR" maxlength="150" (keyup.enter)="onEnterFinished('3', $event)">
                                    </mat-form-field>
                                </div>
                            </ng-container>

                            <div class="s_head">
                                <span i18n="@@vnPostDescriptionTitleH1">Thông tin phí thu hồ sơ/trả kết quả tại nhà</span><br fxShow="true" fxHide.gt-sm>
                            </div>
                            <div class="s_head">
                                <span class='payment-status-vnpost' i18n="@@vnPostDescriptionTitleH2">(Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả)</span>
                            </div>
                            <br fxShow="true" fxHide.gt-sm>
                            <div *ngIf="receiveForm.get('vpSend')?.value" class="feeZero">
                                <span>Phí nộp hồ sơ tại nhà: </span> {{this.feeViettelPostS}} (VNĐ)
                            </div>
                            <div *ngIf="receiveForm.get('vpReceive')?.value" class="feeZero">
                                <span>Phí nhận kết quả hồ sơ tại nhà: </span> {{this.feeViettelPostR}} (VNĐ)
                            </div>
                            <div *ngIf="feeViettelPostS === 0 && feeViettelPostR === 0" class="feeZero">
                                <span i18n>Không có thông tin phí, lệ phí</span>
                            </div>
                        </ng-template>
                        
                        
                        <!-- END ------ ViettelPost -->
                        <ng-template #anotherPost>
                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="selectedLevel === 1">
                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1' && !registerAtHomeBCCI">
                                <mat-label >Tên</mat-label>
                                <input type="text" matInput formControlName="rcName" maxlength="500">
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1' && !registerAtHomeBCCI">
                                <mat-label >Số điện thoại</mat-label>
                                <input type="text" matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter">
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="env?.vnpost?.config === '1' && !registerAtHomeBCCI">
                                <mat-label >Email</mat-label>
                                <input type="email" matInput formControlName="rcEmail" maxlength="500">
                            </mat-form-field>

                            <ng-container *ngIf="this.env?.vnpost?.vnpostConfigV2 == 1 && registerAtHomeBCCI">
                                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                    <div class= "groupbox_header" fxFlex="100">
                                        <span>Địa chỉ bưu cục gửi kết quả đến người dân</span>
                                    </div>
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Tỉnh/TP nhận kết quả</mat-label>
                                        <mat-select formControlName="rcProvinceR" (selectionChange)="rcProvinceChangeR()" required>
                                            <mat-option *ngFor='let provinceOpt of listProvinceForRCR;' value="{{provinceOpt.id}}">
                                                {{provinceOpt.name}}
                                                <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Phường/xã nhận kết quả</mat-label>
                                        <mat-select required formControlName="rcDistrictR" (selectionChange)="rcDistrictChangeR()" [disabled]="this.receiveForm.get('rcProvinceR').value == ''">
                                            <mat-option *ngFor='let districtOpt of listDistrictForRCR;' value="{{districtOpt.id}}">
                                                {{districtOpt.name}}
                                                <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                    <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label >Phường/xã nhận kết quả</mat-label>
                                        <mat-select formControlName="rcVillageR" [disabled]="this.receiveForm.get('rcDistrictR').value == ''">
                                            <mat-option *ngFor='let wardtOpt of listVillageForRCR;' value="{{wardtOpt.id}}">
                                                {{wardtOpt.name}}
                                                <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field> -->

                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                        <mat-label >Địa chỉ chi tiết nơi nhận kết quả</mat-label>
                                        <input type="text" matInput formControlName="customAddressR" maxlength="500">
                                        <!-- quocpa 07/05/2023:IGATESUPP-46164 -->
                                        <mat-error *ngIf="enableValidDetailedAddress && !receiveForm.valid">
                                            Vui lòng nhập Địa chỉ chi tiết nơi nhận kết quả
                                          </mat-error>
                                          <!-- end quocpa 07/05/2023:IGATESUPP-46164 -->
                                    </mat-form-field>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="this.env?.vnpost?.vnpostConfigV2 == 1">
                                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                    <div class= "groupbox_header" fxFlex="100">
                                        <span>
                                            Địa chỉ bưu cục gửi kết quả đến người dân
                                        </span>
                                    </div>
                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label i18n>Tỉnh/TP</mat-label>
                                        <mat-select formControlName="rcProvince" (selectionChange)="rcProvinceChange()" required>
                                            <mat-option *ngFor='let provinceOpt of listProvinceForRC;' value="{{provinceOpt.id}}">
                                                {{provinceOpt.name}}
                                                <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label>Phường/xã</mat-label>
                                        <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()" [disabled]="this.receiveForm.get('rcProvince').value == ''">
                                            <mat-option *ngFor='let districtOpt of listDistrictForRC;' value="{{districtOpt.id}}">
                                                {{districtOpt.name}}
                                                <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                    <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label i18n>Phường/xã</mat-label>
                                        <mat-select required formControlName="rcVillage" [disabled]="this.receiveForm.get('rcDistrict').value == ''">
                                            <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                                                {{wardtOpt.name}}
                                                <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field> -->

                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="env?.vnpost?.config === '1'">
                                        <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                        <input type="text" matInput formControlName="customAddress" maxlength="500">
                                        <!-- quocpa 07/05/2023:IGATESUPP-46164 -->
                                        <mat-error *ngIf="enableValidDetailedAddress && !receiveForm.valid">
                                            Vui lòng nhập Địa chỉ chi tiết
                                          </mat-error>
                                          <!-- end quocpa 07/05/2023:IGATESUPP-46164 -->
                                    </mat-form-field>

                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="env?.vnpost?.config !== '1' || env?.vnpost === undefined || env?.vnpost === null">
                                        <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                        <input type="text" matInput formControlName="customAddress" maxlength="500">
                                    </mat-form-field>
                                </div>
                            </ng-container>
                        </div>
                        </ng-template>

                        <ng-container *ngIf="this.env?.OS_QBH?.isQBH == true && this.env?.OS_QBH?.isGetNotiForm == true">
                          <div class="s_head">
                              <span>Đăng ký nhận thông báo về việc giải quyết hồ sơ</span>
                          </div>
                          <br fxShow="true" fxHide.gt-sm>
                          <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'" fxLayout="column" fxFlex.gt-sm="16.25" fxFlex.gt-xs="49.5" fxFlexOffset="10px" fxFlex='grow'>
                              <mat-checkbox formControlName="rcSendSMS">
                                  Đăng ký nhận thông báo qua SMS
                              </mat-checkbox>

                              <mat-checkbox formControlName="rcReceiveZalo">
                                  Đăng ký nhận thông báo qua Zalo
                              </mat-checkbox>
                              <mat-checkbox formControlName="rcReceiveGmail">
                                  Đăng ký nhận thông báo qua Gmail
                              </mat-checkbox>
                          </div>
                        </ng-container>
                    </form>

                    <div class="s_head" *ngIf="enableRemoveVnpostFeeToAnotherTable == 0 && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0">
                        <span i18n>Thông tin phí, lệ phí</span>
                        <span *ngIf="haveFeeCheck == true && isCheckCitizenPaymentQBH" class='payment-status-vnpost'> <span *ngIf="qbhFormOnlineFee" style="font-weight: bold;"> (Thanh toán khi nộp hồ sơ)</span><span *ngIf="!qbhFormOnlineFee"> (Phí thanh toán khi nộp hồ sơ)</span></span>
                        <span *ngIf="haveFeeCheck == true && !isCheckCitizenPaymentQBH" class='payment-status-vnpost'> <span *ngIf="qbhFormOnlineFee" style="font-weight: bold;"> (Thanh toán trong quá trình xử lý/trả kết quả)</span><span *ngIf="!qbhFormOnlineFee"> (Phí thanh toán trong quá trình xử lý/trả kết quả)</span></span>
                        <span *ngIf="haveFeeCheck == false" class='payment-status-vnpost'> (Không có phí, lệ phí)</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <div class="s_head" *ngIf="this.haveFeeCheck == true && this.qbhFormOnlineFee && !hidePaymentMethod && this.hideDossierFeeCTO == 0">
                        <span i18n>Chọn hình thức thanh toán</span><span class="required">(*)</span>
                        <span class="note" *ngIf="isEnableNotePayment">Khuyến khích doanh nghiệp/cơ sở thanh toán phí trực tuyến</span>
                    </div>
                    <br *ngIf="this.qbhFormOnlineFee" fxShow="true" fxHide.gt-sm>
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="this.haveFeeCheck == true && this.qbhFormOnlineFee && !hidePaymentMethod && this.hideDossierFeeCTO == 0">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow'>
                            <mat-select msInfiniteScroll [(value)]="paymentMethodId" (selectionChange)="onPaymentMethodChoose($event)" (infiniteScroll)="getListPaymentMethod()" [complete]="listPaymentMethodFull == true">
                                <mat-option *ngFor='let paymentMethod of listPaymentMethod;' value="{{paymentMethod.id}}">
                                    {{paymentMethod.name}}
                                    <span *ngIf="paymentMethod.name == undefined || paymentMethod.name == null || paymentMethod.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select>

                        </mat-form-field>
                    </div>
                    <div class="s_head" *ngIf="!isHideInfoPayment && !isUserRQ && !hidePaymentInfor && this.checkPaymentFlatform == true && this.qbhFormOnlineFee && this.hideDossierFeeCTO == 0" fxLayout="column">
                        <p *ngIf="!!this.accoutPayment.id"><span class="fontweight500">Cơ quan thụ hưởng:</span> {{this.accoutPayment.agencyName}}</p>
                        <p *ngIf="!this.accoutPayment.id"><span class="fontweight500">{{this.accoutPayment.name}} </span></p>
                    </div>
                    <br *ngIf="this.qbhFormOnlineFee" fxShow="true" fxHide.gt-sm>
                    <div class="procedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="this.haveFeeCheck == true && enableRemoveVnpostFeeToAnotherTable == 0 && enableApplyOnlineFee==false && this.hideDossierFeeCTO == 0">
                        <mat-table [dataSource]="feeDataSource" fxFlex='grow' class="tblFee">
                            <ng-container matColumnDef="select">
                                <mat-header-cell *matHeaderCellDef></mat-header-cell>
                                <mat-cell *matCellDef="let row">
                                    <mat-checkbox (click)="$event.stopPropagation()"
                                                    (change)="onRowTableChecked($event,row)"
                                                    [checked]="selection.isSelected(row)"
                                                    [disabled]="row.disableCheckbox">
                                    </mat-checkbox>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>
                            <ng-container matColumnDef="procostType">
                                <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                                    {{row.typeName}}
                                    <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="!allowFeeAmountInput && !enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="row.type.quantityEditable != 1">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="!allowFeeAmountInput && enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="row.type.quantityEditable != 1">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="allowFeeAmountInput && !enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="!checkQuantityEditable(row.type.id)">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="checkQuantityEditable(row.type.id)">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container *ngIf="allowFeeAmountInput && enableMappingNumberCopiesFeesQNM" matColumnDef="quantity">
                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                    <div *ngIf="!checkQuantityEditable(row.type.id)">
                                        {{row.quantity}}
                                    </div>
                                    <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="checkQuantityEditable(row.type.id)">
                                        <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                    </mat-form-field>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="cost" class="fee-column" *ngIf="!changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                    <mat-select value="0" (selectionChange)="changeCount(row.stt, $event)" class="select-fee">
                                        <mat-option *ngFor='let cost of row.costCase; let j = index;'value="{{j}}"
                                            title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                            [disabled]="env?.OS_HCM?.priceSelectDisabled == 1 ? true : false">
                                            {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                                        </mat-option>
                                    </mat-select>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="cost" class="fee-column" *ngIf="changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                    <mat-select value="0" (selectionChange)="changeCount(row.stt, $event)" class="select-fee">
                                        <mat-option *ngFor='let cost of row.costCase; let j = index;'value="{{j}}"
                                            title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                            [disabled]="env?.OS_HCM?.priceSelectDisabled == 1 ? true : false">
                                            <span *ngIf = "cost.cost == '0'">Chưa thẩm định </span>
                                            <span *ngIf = "cost.cost !== '0'">{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})</span>
                                        </mat-option>
                                    </mat-select>
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>



                            <ng-container matColumnDef="amount" *ngIf="!changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                    {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
                                <mat-footer-cell *matFooterCellDef class="totalCell" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''">{{totalCost}}</mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="amount" *ngIf="changePaymentContent">
                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                    <span *ngIf = "row.cost == '0'">Chưa thẩm định </span>
                                    <span *ngIf = "row.cost !== '0'">{{row.quantity*row.cost | number }} {{row.monetaryUnit}} </span>
                                     </mat-cell>
                                <mat-footer-cell *matFooterCellDef class="totalCell">
                                    <span *ngIf = "totalCost == '0'">Chưa thẩm định </span>
                                    <span *ngIf = "totalCost !== '0'">{{totalCost}} </span></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="compulsory">
                                <mat-header-cell *matHeaderCellDef i18n>Bắt buộc</mat-header-cell>
                                <ng-container *ngIf="changeLanguageForSCT; then SCTAgency else anotherAgency;"></ng-container>
                                    <ng-template #SCTAgency>
                                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                            <span *ngIf="row?.required !== 1">{{OS_HCM_SCT?.contentTTS}}</span>
                                            <span *ngIf="row?.required === 1">Có</span>
                                        </mat-cell>
                                    </ng-template>
                                    <ng-template #anotherAgency>
                                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                            <span *ngIf="row?.required !== 1">Không</span>
                                            <span *ngIf="row?.required === 1">Có</span>
                                        </mat-cell>
                                    </ng-template>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <ng-container matColumnDef="description">
                                <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                                </mat-cell>
                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                            </ng-container>

                            <mat-header-row *matHeaderRowDef="feeDisplayedColumnsQBH"></mat-header-row>
                            <mat-row *matRowDef="let row; columns: feeDisplayedColumnsQBH;"></mat-row>
                            <div *ngIf="hideTotal == false">
                                <mat-footer-row *matFooterRowDef="feeDisplayedColumnsQBH"></mat-footer-row>
                            </div>
                        </mat-table>
                    </div>

                    <div *ngIf="this.procostConfig.displayHighlightMultiCost == true && this.procostHasMultipleFee && this.hideDossierFeeCTO == 0">
                        <span class="procost-highlight" i18n="@@paymentHasManyFees">(Dòng lệ phí có nhiều mức phí có màu đỏ)</span>
                    </div>
                    <!-- phucnh.it2-IGATESUPP-31658: End -->
                    <div class="s_head" *ngIf="this.haveFeeCheck == true && !this.qbhFormOnlineFee && !hidePaymentMethod && this.hideDossierFeeCTO == 0">
                        <span i18n>Chọn hình thức thanh toán</span><span class="required">(*)</span>
                        <span class="note" *ngIf="isEnableNotePayment">Khuyến khích doanh nghiệp/cơ sở thanh toán phí trực tuyến</span>
                    </div>
                    <br fxShow="true" fxHide.gt-sm>
                    <!-- dossierReceivingKind != this.config.receiveResultsByAddress -->
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="this.haveFeeCheck == true && !this.qbhFormOnlineFee && !hidePaymentMethod && this.hideDossierFeeCTO == 0">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow'>
                            <mat-select msInfiniteScroll [(value)]="paymentMethodId" (selectionChange)="onPaymentMethodChoose($event)" (infiniteScroll)="getListPaymentMethod()" [complete]="listPaymentMethodFull == true">
                                <mat-option *ngFor='let paymentMethod of listPaymentMethod;' value="{{paymentMethod.id}}">
                                    {{paymentMethod.name}}
                                    <span *ngIf="paymentMethod.name == undefined || paymentMethod.name == null || paymentMethod.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select>

                        </mat-form-field>
                    </div>
                    <div class="s_head" *ngIf="!isHideInfoPayment && !isUserRQ && !hidePaymentInfor && this.checkPaymentFlatform == true && !this.qbhFormOnlineFee && this.hideDossierFeeCTO == 0" fxLayout="column">
                        <p *ngIf="!!this.accoutPayment.id"><span class="fontweight500">Cơ quan thụ hưởng:</span> {{this.accoutPayment.agencyName}}</p>
                        <p *ngIf="!this.accoutPayment.id"><span class="fontweight500">{{this.accoutPayment.name}} </span></p>
                        <p><span class="fontweight500">Tài khoản:</span> {{this.accoutPayment.beneficiaryAccount}}</p>
                        <p><span class="fontweight500">Tên tài khoản:</span> {{this.accoutPayment.beneficiaryAccountName}}</p>
                        <p><span class="fontweight500">Mã ngân hàng:</span> {{this.accoutPayment.beneficiaryBankCode}}</p>
                    </div>
                    <div class="ctrl" *ngIf="allowSelectReceipt && allowSelectReceipt.enable && allowSelectReceiptPayment">
                        <div class="s_head ">
                            <span>Đề nghị cá nhân/doanh nghiệp/cơ sở lựa chọn:</span>
                        </div>
                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline captcha" fxLayoutAlign="space-between" style="border-bottom: 1px solid #e0e0e059;margin-bottom: 15px;">
                            <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'" appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <mat-checkbox [checked]="checkReceiptPersion" (change)="receiptPersionChange()">
                                    <span>Xuất biên lai cho cá nhân</span>
                                </mat-checkbox>
                            </div>
                            <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'" appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <mat-checkbox [checked]="checkReceiptCompany" (change)="receiptCompanyChange()">
                                    <span>Xuất biên lai cho doanh nghiệp/cơ sở</span>
                                </mat-checkbox>
                            </div>
                            <hr/>
                        </div>
                    </div>
                    <div class="ctrl">
                        <div class="captcha">
                            <div [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'chkAgreeNew' : 'chkAgree'">
                                <mat-checkbox (change)="changeConfirmStatus($event)">
                                    <span i18n>Tôi chắc chắn rằng các thông tin khai báo trên là đúng sự thật và đồng ý chịu trách nhiệm trước pháp luật về lời khai trên.</span>
                                </mat-checkbox>
                            </div>
                            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="captchaImgage formFieldOutline" *ngIf="this.captcharOpen == 1">
                                <form [formGroup]="captchaForm" (submit)="captchaSubmit()" id="captchaForm" fxFlex='20' fxLayout="row">
                                    <mat-form-field appearance="outline" fxFlex='grow'>
                                        <mat-label i18n>Mã xác nhận</mat-label>
                                        <input matInput formControlName="captcha" required>
                                        <mat-error *ngIf="captcha.invalid" class="error_Msg">
                                            <span i18n>Vui lòng nhập mã xác nhận</span>
                                            <div class="err">
                                                <mat-icon>priority_high</mat-icon>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                </form>
                                <div fxFlex='8' class="chCode">
                                    <div [innerHTML]="captchaImg" class="chIMG" (click)="generateCaptcha()"></div>
                                    <button mat-flat-button (click)="generateCaptcha()">
                                        <mat-icon>cached</mat-icon>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button mat-stroked-button (click)="goBack(stepper)" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" i18n>Quay lại</button>
                        <!-- <button mat-flat-button (click)="confirmPay(stepper); captchaSubmit(); sendVNPostOverLgspHcmAfterPayment()" form="captchaForm" *ngIf="!confirmPayName && isConfirm && this.haveFeeCheck == true && this.checkHcmNotSendVNPostConfig == 0 " [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Thanh toán</button> -->
                        <!-- <button mat-flat-button (click)="confirmPay(stepper); captchaSubmit(); sendVNPostOverLgspHcmAfterPayment()" form="captchaForm" *ngIf="confirmPayName && isConfirm && this.haveFeeCheck == true && this.checkHcmNotSendVNPostConfig == 0 " [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'">{{confirmPayName}}</button> -->
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit(); " form="captchaForm" *ngIf="!confirmPayName && isConfirm && this.haveFeeCheck == true && !this.qbhFormOnlineFee " [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Thanh toán</button>
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit(); " form="captchaForm" *ngIf="!confirmPayName && isConfirm && this.haveFeeCheck == true && this.qbhFormOnlineFee" class="btn_next_qbh">Thanh toán và nộp hồ sơ</button>
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit(); " form="captchaForm" *ngIf="confirmPayName && isConfirm && this.haveFeeCheck == true " [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'">{{confirmPayName}}</button>
                        <button mat-flat-button [disabled]="disableButtonWhenApply && disasbleApplyButton" (click)="confirmPay(stepper); captchaSubmit()" form="captchaForm" *ngIf="isConfirm && this.haveFeeCheck == false" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n>Tiếp tục</button>
                    </div>
                </mat-step>
                <mat-step [label]="labelStep4">
                    <div class="success_head">
                        <p class="title" i18n>Nộp hồ sơ thành công</p>
                        <p class="title" *ngIf="dossierCode"><span i18n>Mã số hồ sơ: </span>{{ dossierCode }}</p>
                        <p class="sub_title" i18n>Hồ sơ đang chờ tiếp nhận</p>
                    </div>
                    <br fxShow="true" fxHide.gt-sm><br>
                    <div class="user_info" fxLayout="row wrap" fxLayout.xs="column" fxLayout.sm="row wrap" *ngIf="listBusinessRegistration.length === 0">
                        <div class="title" fxFlex="grow" i18n>Người nộp hồ sơ</div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Họ tên người nộp:</div>
                            <div *ngIf="userInfo?.fullname">{{ userInfo?.fullname }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Ngày sinh:</div>
                            <div *ngIf="userInfo?.birthday">{{ userInfo?.birthday | date : 'dd/MM/yyyy' }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>CMND:</div>
                            <div *ngIf="userInfo?.identity">{{ userInfo?.identity?.number }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Nơi cấp CMND/ Căn cước:</div>
                            <div *ngIf="userInfo?.identity?.agency != undefined">{{ userInfo?.identity?.agency?.name }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Số điện thoại:</div>
                            <div *ngIf="userInfo?.phoneNumber && userInfo?.phoneNumber.length > 0">{{ userInfo.phoneNumber[0].value }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Email:</div>
                            <div *ngIf="userInfo?.email && userInfo?.email.length > 0">{{ userInfo.email[0].value }}</div>
                        </div>
                        <div class="item" fxFlex="grow">
                            <div i18n>Địa chỉ:</div>
                            <div *ngIf="userInfo?.address && userInfo?.address.length > 0">{{ userInfo.address[0].address }}</div>
                        </div>
                    </div>
                    <div class="user_info" fxLayout="row wrap" fxLayout.xs="column" fxLayout.sm="row wrap" *ngIf="listBusinessRegistration.length > 0">
                        <div class="title" fxFlex="grow">Thông tin hồ sơ</div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Tên doanh nghiệp:</div>
                            <div>{{ listBusinessRegistration[0].name }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Ngày sinh:</div>
                            <div *ngIf="userInfo.birthday">{{ userInfo.birthday | date : 'dd/MM/yyyy' }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Nơi cấp CMND/ Căn cước:</div>
                            <div *ngIf="userInfo.identity.agency != undefined">{{ userInfo.identity.agency.name }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Số điện thoại:</div>
                            <div *ngIf="userInfo.phoneNumber.length > 0">{{ userInfo.phoneNumber[0].value }}</div>
                        </div>
                        <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                            <div i18n>Email:</div>
                            <div *ngIf="userInfo.email.length > 0">{{ userInfo.email[0].value }}</div>
                        </div>
                        <div class="item" fxFlex="grow">
                            <div i18n>Địa chỉ:</div>
                            <div>{{ listBusinessRegistration[0].address }}</div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="s_head mb-1">
                            <span i18n>Thành phần hồ sơ</span>
                        </div>
                        <div class="rsProcedureForm" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                            <mat-table [dataSource]="formDataSource" fxFlex='grow' class="tblForm">
                                <ng-container matColumnDef="name">
                                    <mat-header-cell *matHeaderCellDef i18n>Tên giấy tờ</mat-header-cell>
                                    <mat-cell *matCellDef="let row" data-label="Tên giấy tờ">
                                        {{row.procedureForm.name}}
                                        <span *ngIf="row.procedureForm.name == undefined || row.procedureForm.name == null || row.procedureForm.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="quantity">
                                    <mat-header-cell *matHeaderCellDef i18n>Số lượng/ loại bản</mat-header-cell>
                                    <mat-cell *matCellDef="let row" data-label="Số lượng/ loại bản">
                                        {{row.detail.quantity}} {{row.detail.type.name}} </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="file">
                                    <mat-header-cell *matHeaderCellDef i18n>Tệp tin</mat-header-cell>
                                    <mat-cell *matCellDef="let row" data-label="Tệp tin">
                                        <div *ngIf="row.file.length > 1">
                                            <span *ngFor="let file of row.file; let i = index;">
                                                <button mat-button *ngIf="i == 1" (click)="viewFile(file.id)">
                                                    <mat-icon class="attachment">attachment</mat-icon>
                                                    {{ file.filename }}
                                                </button>
                                            </span>
                                            <button mat-icon-button class="viewMoreFiles" (click)="viewListFiles(row.file)">
                                                <mat-icon>more_horiz</mat-icon>
                                            </button>
                                        </div>
                                        <div *ngIf="row.file.length == 1">
                                            <span *ngFor="let file of row.file;">
                                                <button mat-button (click)="viewFile(file.id)">
                                                    <mat-icon class="attachment">attachment</mat-icon>
                                                    {{ file.filename }}
                                                </button>
                                            </span>
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <mat-header-row *matHeaderRowDef="formDisplayedColumns"></mat-header-row>
                                <mat-row *matRowDef="let row; columns: formDisplayedColumns;"></mat-row>

                            </mat-table>
                        </div>
                    </div>
                    <div class="mt-2" *ngIf="!changeStepApplyDossier && this.hideDossierFeeCTO == 0">
                        <div class="s_head mb-1">
                            <span i18n>Lệ phí hồ sơ</span>
                        </div>
                        <div class="rsProcedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                            <mat-table [dataSource]="feeDataSource" fxFlex='grow' class="tblFee">
                                <ng-container matColumnDef="procostType">
                                    <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                                        {{row.typeName}}
                                        <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                                </ng-container>

                                <ng-container *ngIf="!allowFeeAmountInput" matColumnDef="quantity">
                                    <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                        <div *ngIf="row.type.quantityEditable != 1">
                                            {{row.quantity}}
                                        </div>
                                        <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="row.type.quantityEditable == 1">
                                            <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                        </mat-form-field>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container *ngIf="allowFeeAmountInput" matColumnDef="quantity">
                                    <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                        <div *ngIf="!checkQuantityEditable(row.type.id)">
                                            {{row.quantity}}
                                        </div>
                                        <mat-form-field appearance="outline" class="quantity" fxFlex.gt-sm='5' *ngIf="checkQuantityEditable(row.type.id)">
                                            <input matInput value="{{ row.quantity }}" (input)="onFeeDossierQuantityChange(row.id, $event.target.value)" type="number" id="feeQuantityDossier_{{row.id}}">
                                        </mat-form-field>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="cost" *ngIf="changePaymentContent">
                                    <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                        <span *ngIf = "row.cost == '0'">Chưa thẩm định </span>
                                        <span *ngIf = "row.cost !== '0'">{{row.cost | number}} {{row.monetaryUnit}}  </span>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="cost" *ngIf="!changePaymentContent">
                                    <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí"> {{row.cost | number}} {{row.monetaryUnit}} </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="amount" *ngIf="changePaymentContent">
                                    <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                        <span *ngIf = "row.cost == '0'">Chưa thẩm định </span>
                                        <span *ngIf = "row.cost !== '0'">{{row.quantity*row.cost | number }} {{row.monetaryUnit}} </span>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef class="totalCell" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''">{{totalCost}}</mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="amount" *ngIf="!changePaymentContent">
                                    <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                        {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
                                    <mat-footer-cell *matFooterCellDef class="totalCell">
                                        <span *ngIf = "totalCost == '0'">Chưa thẩm định </span>
                                        <span *ngIf = "totalCost !== '0'">{{totalCost}} </span>
                                        </mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="compulsory">
                                    <mat-header-cell *matHeaderCellDef i18n>Bắt buộc</mat-header-cell>
                                    <ng-container *ngIf="changeLanguageForSCT; then SCTAgency else anotherAgency;"></ng-container>
                                        <ng-template #SCTAgency>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                                <span *ngIf="row?.required !== 1">{{OS_HCM_SCT?.contentTTS}}</span>
                                                <span *ngIf="row?.required === 1">Có</span>
                                            </mat-cell>
                                        </ng-template>
                                        <ng-template #anotherAgency>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Bắt buộc">
                                                <span *ngIf="row?.required !== 1">Không</span>
                                                <span *ngIf="row?.required === 1">Có</span>
                                            </mat-cell>
                                        </ng-template>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <ng-container matColumnDef="description">
                                    <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả">
                                        {{row.description}} </mat-cell>
                                    <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                </ng-container>

                                <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                                <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                                <div *ngIf="hideTotal == false">
                                    <mat-footer-row *matFooterRowDef="feeDisplayedColumns"></mat-footer-row>
                                </div>
                            </mat-table>
                        </div>
                    </div>
                    <div class="ctrl">
                        <button mat-stroked-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" (click)="update()" i18n>Cập nhật</button>
                        <button mat-stroked-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" (click)="cancel()" i18n>Huỷ</button>
                        <button mat-flat-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_next_new' : 'btn_next'" i18n (click)="redirectMyDossier()">Đồng ý</button>
                        <button mat-stroked-button [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'btn_prev_new' : 'btn_prev'" *ngIf="receivingBallotFile != '' && !changeStepApplyDossier" (click)="createPrintBill(receivingBallotFile)" i18n>In phiếu nộp hồ sơ</button>
                    </div>
                </mat-step>
            </mat-horizontal-stepper>
        </div>
    </div>
</div>
