<div class="all-content">
    <div #ref>
      <ng-content></ng-content>
    </div>
    <span *ngIf="ref.childNodes.length == 0">
      <button (click)="onClickCitizenStorage()" class="btnCitizenStorage"
        *ngIf="this.enableNotarizationRepository == true"
        [ngClass]="this.enableNotarizationRepository ? 'custom-color' : 'disable-color-is'" mat-flat-button>
        <mat-icon>input</mat-icon>
        <span>Lấy giấy tờ từ kho công dân<noscript></noscript></span>
      </button>
      <button (click)="onClick()" class="btnPrimary" [ngClass]="ShowTheNewInterfaceDVCTP === 1 ? 'custom-color' : ''" mat-flat-button>
          <mat-icon>input</mat-icon>
          <span>Lấy giấy tờ từ kho</span>
      </button>
    </span>
</div>
