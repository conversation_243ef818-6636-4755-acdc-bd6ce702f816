{"keycloakDebug": true, "keycloakOptions": {"config": {"url": "${KEYCLOAK_URL}", "realm": "${KEYCLOAK_REALM}", "clientId": "${KEYCLOAK_CLIENT_ID}"}, "initOptions": {"onLoad": "check-sso", "checkLoginIframe": false}, "bearerExcludedUrls": ["/sy/app-deployment"], "loadUserProfileAtStartUp": true}, "insufficientPermissionRouterLink": "error/insufficient-permission", "apiProviders": {"digo": {"rootUrl": "${DIGO_API_GATEWAY}", "services": {"basedata": {"path": "ba"}, "basecat": {"path": "bt"}, "fileman": {"path": "fi"}, "human": {"path": "hu"}, "postman": {"path": "po"}, "logman": {"path": "lo"}, "surfeed": {"path": "su"}, "messenger": {"path": "me"}, "padman": {"path": "pa"}, "adapter": {"path": "integration"}, "basepad": {"path": "bd"}, "reporter": {"path": "re"}, "bpm": {"path": "bpm"}, "modeling": {"path": "modeling-service"}, "chatbotadm": {"path": "botadm"}, "upload": {"path": "upload"}, "storage": {"path": "storage"}, "statistics": {"path": "statistics"}}}}, "ancestorPlaceId": "${ANCESTOR_PLACE_ID}", "tagProvince": "${TAG_PROVINCE}", "tagDistrict": "${TAG_DISTRICT}", "tagWards": "${TAG_WARDS}", "registerURL": "${REGISTER_URL}", "cloudStaticURL": "${STATIC_URL}", "webAccountURL": "${WEB_ACCOUNT_URL}", "birtviewerURL": "${BIRT_VIEWER_URL}", "supportFile": ["pdf", "doc", "docx", "xls", "xlsx", "txt"], "qnaURL": "${QNA_URL}", "defaultFormIO": "${DEFAULT_FORM_IO}", "formioURL": "${FORM_IO_URL}", "deploymentId": "${DEPLOYMENT_ID}", "deploymentIdCloud": "${DEPLOYMENT_ID_ClOUD}", "deploymentUrl": "${DIGO_DEPLOYMENT_URL}", "appCode": "${APP_CODE}", "subsystemId": "${SUBSYSTEM_ID}", "tutorialLink": "${TUTORIAL_LINK}", "guideTagId": "${GUIDE_TAG_ID}", "applyOnlineStepCategoryId": "${APPLY_ONLINE_STEP_CATEGORY_ID}", "procedureLevelCategoryId": "${PROCEDURE_LEVEL_CATEGORY_ID}", "documentTypeCategoryId": "${DOCUMENT_TYPE_CATEGORY_ID}", "implementerCategoryId": "${IMPLEMENTER_CATEGORY_ID}", "citizenId": "${CITIZEN_ID}", "enterpriseId": "${ENTERPRISE_ID}", "districtTagId": "${DISTRICT_TAG_ID}", "villageTagId": "${VILLAGE_TAG_ID}", "departmentTagId": "${DEPARTMENT_TAG_ID}", "receivingBallotTagId": "${RECEIVING_BALLOT_TAG_ID}", "procedureLevel2Id": "${PROCEDURE_LEVEL_2_ID}", "businessRegistrationConfigId": "${BUSINESS_REGISTRATION_CONFIG_ID}", "webPadsvcSubsystemId": "5f7c16069abb62f511880006", "agencyLevelCategoryId": "5f3a491c4e1bd312a6f00009", "titleChatBot": "${TITLE_CHATBOT}", "urlWebChatBot": "${URL_WEB_CHATBOT}", "urlChatBotRasa": "${URL_CHATBOT_RASA}", "chatbotConfigure": "${CHATBOT_CONFIGURE}", "configuratingChatbotId": "${DIGO_CONFIGURATING_CHATBOT_ID}", "chatbotRestartMinute": "${CHATBOT_RESTART_MINUTE}", "domainFileChatbot": "${DOMAIN_FILE_CHATBOT}", "subsystemWebPadsvcId": "5f7c16069abb62f511880006", "languageDefaultId": 228, "languageDefault": "vi", "reloadTimeout": 2000, "expiredTime": 3000, "agencyMinistryLevelId": "5ff6b1a706d0e31c6bf13e09", "budgetCodeConfigId": "61c2df9a3957f946770ffb70", "translatePaginator": ["Số dòng", "<PERSON><PERSON>", "<PERSON><PERSON> tr<PERSON>", "<PERSON><PERSON> ti<PERSON><PERSON> theo", "<PERSON><PERSON> cu<PERSON>i", "c<PERSON>a"], "pageSizeOptions": [5, 10, 20, 50], "searchAfterStopTyping": 800, "fileUnits": ["bytes", "KB", "MB", "GB", "TB", "PB"], "regValidators": "/[-a-zA-Z0-9@:%_+.~#?&//=]{2,256}.[a-z]{2,4}\b(/[-a-zA-Z0-9@:%_+.~#?&//=]*)?/gi", "procedureFormAcceptFileExtension": [".DOC", ".DOCX", ".PDF", ".XLS", ".XLSX", ".TXT", ".JPG", ".JPEG", ".PNG", ".DWG"], "procedureFormAcceptFileType": ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/pdf", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "text/plain", "image/jpeg", "image/png"], "procedureFormMaxFileSize": "${PROCEDURE_FORM_MAX_FILESIZE}", "rootAgency": {"id": "${ROOT_AGENCY_ID}", "code": "${ROOT_AGENCY_CODE}", "trans": {"vi": {"name": "${ROOT_AGENCY_NAME_VI}"}, "en": {"name": "${ROOT_AGENCY_NAME_EN}"}}, "provinceId": "${ROOT_PROVINCE_ID}"}, "defaultUserAvatar": "${DEFAULT_USER_AVATAR}", "defaultLogo": "${DEFAULT_LOGO}", "defaultBanner": "${DEFAULT_BANNER}", "getPublicTokenURL": "${PUBLIC_TOKEN_URL}", "GRANT_TYPE": "${PUBLIC_GRANT_TYPE}", "CLIENT_ID": "${OAUTH2_CLIENT_ID}", "SCOPE": "${OAUTH2_SCOPE}", "CLIENT_SECRET": "${CLIENT_SECRET}", "siteTitle": {"vi": "${VI_SITETITLE}", "en": "${EN_SITETITLE}"}, "dossierEmailSMSZaloConfig": {"sms": {"enable": "${EMAIL_SMS_ZALO_CONFIG_Enable}", "message": {"vi": "${SMS_APPLY_SUCCESSFULLY_VI}", "en": "${SMS_APPLY_SUCCESSFULLY_EN}"}, "characterLimit": 150}}, "searchMappingDataType": {"district": "${MAPPING_DATATYPE_DISTRICT}", "province": "${MAPPING_DATATYPE_PROVINCE}", "agency": "${MAPPING_DATATYPE_AGENCY}", "ward": "${MAPPING_DATATYPE_WARD}"}, "placeProvinceTypeId": "${PLACE_PROVINCE_TYPE_ID}", "placeDistrictTypeId": "${PLACE_DISTRICT_TYPE_ID}", "placeWardTypeId": "${PLACE_WARD_TYPE_ID}", "nationId": "${NATION_ID}", "reporterLocation": "${REPORTER_LOCATION}", "mapOption": "mapbox", "scale": "r169:640,r169:320", "onlinePaymentMethodId": "5f7fca9fb80e603d5300dcf5", "publicFileUrl": "${PUBLIC_FILE_URL}", "igateSubsystemId": "5f7c16069abb62f511880003", "signPositionDigitalSignature": "10,10,150,70", "locationDigitalSignature": "<PERSON><PERSON> t<PERSON>n", "messageDisplayDigitalSignature": "Vui long nhap PIN ky so", "chatbotExcludeUrls": "${DIGO_CHATBOT_EXCLUDE_URLS}"}