import {Component, Inject, Input, OnInit} from '@angular/core';
import {MatTableDataSource} from "@angular/material/table";
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from "@angular/material/dialog";
import {FilemanService} from "data/service/svc-fileman/fileman.service";
import { ViewSignHistoryDialogComponent } from './view-sign-history-dialog/view-sign-history-dialog.component';

@Component({
  selector: 'view-sign-history',
  templateUrl: './view-sign-history.component.html',
  styleUrls: ['./view-sign-history.component.scss']
})
export class ViewSignHistoryComponent implements OnInit {
  @Input() fileId: any;
  constructor(
    private filemanService: FilemanService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
  }

  openHistory() {
    this.filemanService.getSignHistories(this.fileId).subscribe((data) => {
      const dialogRef = this.dialog.open(ViewSignHistoryDialogComponent, {
        width: '1200px',
        data,
        disableClose: true,
        autoFocus: false
      });
    });
  }
}
