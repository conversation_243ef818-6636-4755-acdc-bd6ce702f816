import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { catchError, tap } from 'rxjs/operators';
@Injectable({
  providedIn: 'root'
})
export class GetFormOriginItemService {
  private getProcedureURL = this.apiProviderService.getUrl('digo', 'basepad');
  private getAdapterURL = this.apiProviderService.getUrl('digo', 'adapter');
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  getComponentsProcedure(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcedureURL + '/procedure/' + id+'/--component', {headers});
  }

  //IGATESUPP-126962
  downloadFilesFromCitizenStorage(body: any): Observable<any> {
    const url = this.getAdapterURL + `/integrated-citizen-storage/downloadFiles`;
    return this.http.post<any>(url, body).pipe(
      tap(response => {
        console.log('Dữ liệu nhận từ API:', response);
      }),
      catchError(error => {
        console.error('Lỗi khi lấy dữ liệu:', error);
        return throwError(error);
      })
    );
  }

  getProcedureProcessDefinitionDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcedureURL + '/procedure-process-definition/' + id, { headers });
  }

}
