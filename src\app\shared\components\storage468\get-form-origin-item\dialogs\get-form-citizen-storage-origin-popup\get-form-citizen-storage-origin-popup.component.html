<div class="all-content">
  <button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
  </button>
  <span class="sppName"><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> tờ từ kho cá nhân</span>
  <br>
  <br>
  <div *ngIf="this.enableNotarizationRepository; else noAccessMessage" class="form-section">
    <div class="ho-so-widget">
      <mat-card>
        <mat-card-content>
          <div class="info-container">
            <label class="info-label">Chủ hồ sơ:</label>
            <span class="info-content">{{ this.TenChuHoSo }} (CCCD/CMND/MST: {{this.SoDinhDanhChuHoSo}})</span>
          </div>
          <div class="info-container">
            <label class="info-label">Tên thành phần hồ sơ:</label>
            <span class="info-content">{{ this.tenThanhPhanHS }}</span>
          </div>
          <div class="info-container">
            <label class="info-label"><PERSON><PERSON> thành phần hồ sơ:</label>
            <span class="info-content">{{ this.maThanhPhanHS }}</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
    <div fxLayout="row" fxLayoutAlign="center">
      <div fxFlex="grow">
        <div class="tbl mat-elevation-z2" *ngIf="dataHasValue; else noValueMessage" class="container"
          [ngClass]="{'hidden-section': !dataHasValue}">
          <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

            <!-- Cột số thứ tự -->
            <ng-container matColumnDef="stt">
              <th mat-header-cell *matHeaderCellDef> STT </th>
              <td mat-cell *matCellDef="let element"> {{ element.stt }} </td>
            </ng-container>

            <!-- Cột mã hồ sơ -->
            <ng-container matColumnDef="maHoSo">
              <th mat-header-cell *matHeaderCellDef> Mã hồ sơ </th>
              <td mat-cell *matCellDef="let element"> {{ maThanhPhanHS }} </td>
            </ng-container>

            <!-- Cột số giấy tờ -->
            <ng-container matColumnDef="soGiayTo">
              <th mat-header-cell *matHeaderCellDef> Số giấy tờ </th>
              <td mat-cell *matCellDef="let element"> {{ maThanhPhanHS }} </td>
            </ng-container>

            <!-- Cột tập tin -->
            <ng-container matColumnDef="tapTin">
              <th mat-header-cell *matHeaderCellDef> Tập tin </th>
              <td mat-cell *matCellDef="let element">
                <a *ngIf="element.linkTapTin" [href]="element.linkTapTin" target="_blank" rel="noopener noreferrer">
                  {{ element.tapTin }}
                </a>
                <span *ngIf="!element.linkTapTin">Không có tập tin</span>
              </td>
            </ng-container>

            <!-- Cột chọn giấy tờ (Checkbox) -->
            <ng-container matColumnDef="checked">
              <th mat-header-cell *matHeaderCellDef> Chọn giấy tờ </th>
              <td mat-cell *matCellDef="let element">
                <mat-checkbox [(ngModel)]="element.checked"></mat-checkbox>
              </td>
            </ng-container>

            <!-- Header -->
            <tr mat-header-row *matHeaderRowDef="['stt', 'maHoSo', 'soGiayTo', 'tapTin', 'checked']"></tr>

            <!-- Dòng dữ liệu -->
            <tr mat-row *matRowDef="let row; columns: ['stt', 'maHoSo', 'soGiayTo', 'tapTin', 'checked']"></tr>

            <!-- Dòng không có dữ liệu -->
            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell text-center" colspan="5">Không tìm thấy dữ liệu</td>
            </tr>

          </table>
          <div fxLayoutAlign="center">
            <div #ref>
              <ng-content></ng-content>
            </div>
            <span *ngIf="ref.childNodes.length == 0">
              <button (click)="onSelectFile()" class="btnUploadFile" mat-flat-button>
                <mat-icon>input</mat-icon>
                <span>Lấy giấy tờ đã chọn</span>
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #noAccessMessage>
  <label class="init-label">Bạn không có quyền truy cập</label>
</ng-template>

<ng-template #noValueMessage>
  <div class="center-container">
    <label class="warning-label">Không tìm tài liệu</label>
  </div>
</ng-template>