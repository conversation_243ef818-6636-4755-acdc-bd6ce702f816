import { BrowserModule } from '@angular/platform-browser';
import { NgModule, APP_INITIALIZER } from '@angular/core';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CoreModule } from './core/core.module';
import { SharedModule } from './shared/shared.module';
import { KeycloakService, KeycloakAngularModule } from 'keycloak-angular';
import { initializer } from './app-init';
import { EnvService } from './core/service/env.service';
import { DatePipe } from '@angular/common';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { ChooseFormOrginComponent } from 'shared/components/storage468/get-form-storage468/choose-form-orgin/choose-form-orgin.component';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { GetFormOriginPopupComponent } from './shared/components/storage468/get-form-origin-item/dialogs/get-form-origin-popup/get-form-origin-popup.component';
import { GetFormCitizenStorageOriginPopupComponent } from 'src/app/shared/components/storage468/get-form-origin-item/dialogs/get-form-citizen-storage-origin-popup/get-form-citizen-storage-origin-popup.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import {MatButtonModule} from '@angular/material/button';

@NgModule({
  declarations: [
    AppComponent,
    ChooseFormOrginComponent,
    GetFormOriginPopupComponent,
    GetFormCitizenStorageOriginPopupComponent //CTO-IGATESUPP-126962
  ],
  imports: [
    BrowserModule,
    KeycloakAngularModule,
    CoreModule,
    SharedModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    CKEditorModule,
    MatButtonModule,
    MatDialogModule,
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: initializer,
      multi: true,
      deps: [KeycloakService, EnvService]
    },
    DatePipe,
    { provide: MAT_DATE_LOCALE, useValue: 'en-GB' }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
