<div fxLayout="column" fxLayoutAlign="center" class="container">
    <div fxFlex='100' class="title">
        <span i18n="@@enterPhoneNumber">Nhập số điện thoại</span>
    </div>
    <div fxFlex='100' *ngIf="this.loginForm.invalid">
        <span class="error" i18n="@@invalidPhoneNumber">Số điện thoại không hợp lệ</span>
    </div>
    <div fxFlex='100'>
        <form [formGroup]="loginForm" class="loginForm" fxLayout="column">
            <mat-form-field appearance="outline" fxFlex='80'>
                <mat-label i18n="@@phoneNumber">Số điện thoại</mat-label>
                <input matInput formControlName="tel" type="tel" required>
            </mat-form-field>
        </form>
    </div>
    <div fxFlex='100' fxLayout="row" fxLayoutAlign="center" fxLayoutGap="1rem">
        <button mat-flat-button class="btn" color="primary" fxFlex='30' (click)="onConfirm()">
            <mat-icon>key</mat-icon><span i18n="@@confirm">Xác nhận</span>
        </button>
        <button mat-flat-button class="btn" color="warn" fxFlex='30' (click)="onDismiss()">
            <mat-icon>cancel</mat-icon><span i18n="@@cancel">Hủy</span>
        </button>
    </div>
</div>