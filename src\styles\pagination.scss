::ng-deep .pagination {
    padding: 1em;
    display: flex;

    .temp-arr {
        display: none;
    }

    .page-size {
        .mat-form-field-appearance-outline {
            .mat-form-field-outline {
                color: #e8e8e8;
            }

            .mat-form-field-wrapper {
                width: 4.5em;
                margin: 0 0.5em;
            }

            .mat-form-field-infix {
                padding: 0.5em 0;
            }

            .mat-select-arrow-wrapper {
                padding-top: 0.5em;
            }

            .mat-form-field-flex {
                padding: 0 0.5em 0.5em 0.5em;
            }
        }
    }

    .control {
        text-align: right;
        margin-left: auto;

        .ngx-pagination {
            a {
                outline: unset;
                background-color: #e8e8e8;
                border-radius: 4px;
                margin: 0 .2em;
            }

            .current {
                background: #ce7a58;
                border-radius: 4px;
                margin: 0 .2em;
            }

            .pagination-first-page a::before,
            .pagination-first-page.disabled::before {
                font-family: "Material Icons";
                content: "first_page";
                vertical-align: middle;
                transform: scale(1.5);
                margin-right: 0;
                color: #1E2F41;
            }

            .pagination-previous a::before,
            .pagination-previous.disabled::before {
                font-family: "Material Icons";
                content: "chevron_left";
                vertical-align: middle;
                transform: scale(1.5);
                margin-right: 0;
                color: #1E2F41;
            }

            .pagination-next a::after,
            .pagination-next.disabled::after {
                font-family: "Material Icons";
                content: "chevron_right";
                vertical-align: middle;
                transform: scale(1.5);
                margin-left: 0;
                color: #1E2F41;
            }
        }
    }
}
